<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="600"
             x:Class="HeroYulgang.Views.OnlinePlayersView"
             x:CompileBindings="False">

    <Grid RowDefinitions="Auto,Auto,*,Auto">
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Danh sách Players Online" Classes="header" Margin="16"/>

        <!-- Controls -->
        <Grid Grid.Row="1" ColumnDefinitions="*,Auto,Auto,Auto" Margin="16,0,16,16">
            <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="10">
                <TextBlock Text="Tìm kiếm:" VerticalAlignment="Center"/>
                <TextBox x:Name="SearchTextBox" Width="200" Watermark="Nhập tên player..."/>
                <Button x:Name="SearchButton" Content="Tìm" Classes="primary"/>
                <Button x:Name="RefreshButton" Content="Làm mới" Classes="secondary"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Grid.ColumnSpan="3" Orientation="Horizontal" Spacing="10" HorizontalAlignment="Right">
                <TextBlock Text="Tổng số:" VerticalAlignment="Center"/>
                <TextBlock x:Name="TotalPlayersText" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                <TextBlock Text="players" VerticalAlignment="Center"/>
            </StackPanel>
        </Grid>

        <!-- Players List -->
        <Border Grid.Row="2" Margin="16,0,16,16" BorderBrush="Gray" BorderThickness="1" CornerRadius="4">
            <Grid RowDefinitions="Auto,*">
                <!-- Header -->
                <Grid Grid.Row="0" ColumnDefinitions="80,150,80,120,100,120,100,100,*"
                      Background="#000" Height="32">
                    <TextBlock Grid.Column="0" Text="ID" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="1" Text="Tên" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="2" Text="Cấp độ" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="3" Text="Job" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="4" Text="Job Level" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="5" Text="Map" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="6" Text="Tọa độ" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="7" Text="Online từ" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                    <TextBlock Grid.Column="8" Text="SessionID" FontWeight="Bold" VerticalAlignment="Center" Margin="8,0"/>
                </Grid>

                <!-- Data -->
                <ListBox Grid.Row="1" x:Name="PlayersDataGrid" Background="Black">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid ColumnDefinitions="80,150,80,120,100,120,100,100,*" Height="28">
                                <TextBlock Grid.Column="0" Text="{Binding AccountID}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="1" Text="{Binding CharacterName}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="2" Text="{Binding Player_Level}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="3" Text="{Binding Player_Job}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="4" Text="{Binding Player_Job_level}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="5" Text="{Binding NhanVatToaDo_BanDo}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="6" Text="{Binding NhanVatToaDo_X}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="7" Text="{Binding OnlineTime, StringFormat=\{0:HH:mm:ss\}}" VerticalAlignment="Center" Margin="8,0"/>
                                <TextBlock Grid.Column="8" Text="{Binding SessionID}" VerticalAlignment="Center" Margin="8,0"/>
                            </Grid>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Grid>
        </Border>

        <!-- Footer with actions -->
        <Border Grid.Row="3" Classes="panel" Margin="16" Background="Black">
            <StackPanel Orientation="Horizontal" Spacing="10" Background="Black">
                <Button x:Name="KickPlayerButton" Content="Kick Player" Classes="danger" IsEnabled="False"/>
                <Button x:Name="SendMessageButton" Content="Gửi tin nhắn" Classes="primary" IsEnabled="False"/>
                <Button x:Name="TeleportToPlayerButton" Content="Teleport đến" Classes="secondary" IsEnabled="False"/>
                <Button x:Name="ViewPlayerDetailsButton" Content="Xem chi tiết" Classes="primary" IsEnabled="False"/>

                <Separator/>

                <Button x:Name="ExportPlayersButton" Content="Xuất danh sách" Classes="secondary"/>
                <Button x:Name="BroadcastMessageButton" Content="Thông báo toàn server" Classes="warning"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
