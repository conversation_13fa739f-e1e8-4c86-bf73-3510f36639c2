using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Threading;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Views
{
    public partial class LivePlayersView : UserControl
    {
        public ObservableCollection<Players> Players { get; set; }
        private Players? _selectedPlayer;
        private System.Threading.Timer? _updateTimer;
        private int _peakTodayCount = 0;
        private DateTime _peakTime = DateTime.Now;
        private int _lastKnownCount = 0;

        public LivePlayersView()
        {
            InitializeComponent();
            Players = new ObservableCollection<Players>();
            AutoUpdateCheckBox.IsChecked = true;
            SetupEventHandlers();
            LoadPlayers();
            StartAutoUpdate();
        }

        private void SetupEventHandlers()
        {
            SearchButton.Click += OnSearchClick;
            ClearSearchButton.Click += OnClearSearchClick;
            RefreshButton.Click += OnRefreshClick;
            PlayersDataGrid.SelectionChanged += OnPlayerSelectionChanged;
            AutoUpdateCheckBox.Checked += OnAutoUpdateChanged;
            AutoUpdateCheckBox.Unchecked += OnAutoUpdateChanged;

            KickPlayerButton.Click += OnKickPlayerClick;
            SendMessageButton.Click += OnSendMessageClick;
            TeleportToPlayerButton.Click += OnTeleportToPlayerClick;
            ViewPlayerDetailsButton.Click += OnViewPlayerDetailsClick;
            ExportPlayersButton.Click += OnExportPlayersClick;
            BroadcastMessageButton.Click += OnBroadcastMessageClick;
        }

        private void StartAutoUpdate()
        {
            if (AutoUpdateCheckBox?.IsChecked == true)
            {
                StopAutoUpdate(); // Đảm bảo timer cũ được dừng trước khi tạo mới
                _updateTimer = new System.Threading.Timer(async _ =>
                {
                    try
                    {
                        await Dispatcher.UIThread.InvokeAsync(() =>
                        {
                            LoadPlayers();
                            //Logger.Instance.Info("Đã tự động cập nhật danh sách players");
                        });
                    }
                    catch (Exception ex)
                    {
                        Logger.Instance.Error($"Lỗi trong timer auto-update: {ex.Message}");
                    }
                }, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
                Logger.Instance.Info("Bắt đầu tự động cập nhật mỗi 5 giây");
            }
        }

        private void StopAutoUpdate()
        {
            _updateTimer?.Dispose();
            _updateTimer = null;
        }

        private void UpdateStatusIndicator()
        {
            try
            {
                var isActive = World.allConnectedChars.Count > 0;
                StatusIndicator.Background = isActive ?
                    Avalonia.Media.Brush.Parse("#10B981") :
                    Avalonia.Media.Brush.Parse("#EF4444");
                StatusText.Text = isActive ? "Live" : "No Players";
                StatusText.Foreground = isActive ?
                    Avalonia.Media.Brush.Parse("#10B981") :
                    Avalonia.Media.Brush.Parse("#EF4444");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi cập nhật status indicator: {ex.Message}");
            }
        }

        private void LoadPlayers()
        {
            try
            {
                // Tạo danh sách tạm để so sánh
                var currentPlayers = new HashSet<Players>(World.allConnectedChars.Values);

                // Xóa các player không còn trong allConnectedChars
                for (int i = Players.Count - 1; i >= 0; i--)
                {
                    if (!currentPlayers.Contains(Players[i]))
                    {
                        Players.RemoveAt(i);
                    }
                }

                // Thêm các player mới
                foreach (var player in currentPlayers)
                {
                    if (!Players.Contains(player))
                    {
                        Players.Add(player);
                    }
                }

                PlayersDataGrid.ItemsSource = Players; // Gán lại ItemsSource (thường không cần nếu ObservableCollection đã bind)
                UpdateStatistics();
                UpdateStatusIndicator();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải danh sách players: {ex.Message}");
            }
        }

        private void UpdateStatistics()
        {
            var currentCount = Players.Count;
            
            // Update total players
            TotalPlayersText.Text = currentCount.ToString();
            
            // Update active sessions (same as total for now)
            ActiveSessionsText.Text = currentCount.ToString();
            
            // Update peak today
            if (currentCount > _peakTodayCount)
            {
                _peakTodayCount = currentCount;
                _peakTime = DateTime.Now;
            }
            PeakTodayText.Text = _peakTodayCount.ToString();
            PeakTimeText.Text = $"at {_peakTime:HH:mm}";
            
            // Calculate average level
            if (Players.Count > 0)
            {
                var avgLevel = Players.Average(p => p.Player_Level);
                AverageLevelText.Text = avgLevel.ToString("F1");
            }
            else
            {
                AverageLevelText.Text = "0";
            }
        }

        private void OnSearchClick(object? sender, RoutedEventArgs e)
        {
            PerformSearch();
        }

        private void OnClearSearchClick(object? sender, RoutedEventArgs e)
        {
            SearchTextBox.Text = "";
            PlayersDataGrid.ItemsSource = Players;
        }

        private void PerformSearch()
        {
            var searchText = SearchTextBox.Text?.Trim().ToLower();
            if (string.IsNullOrEmpty(searchText))
            {
                PlayersDataGrid.ItemsSource = Players;
                return;
            }

            var filteredPlayers = Players.Where(p =>
                p.CharacterName.ToLower().Contains(searchText) ||
                p.AccountID.ToString().Contains(searchText) ||
                p.SessionID.ToString().Contains(searchText) ||
                p.Player_Job.ToString().ToLower().Contains(searchText)
            ).ToList();

            PlayersDataGrid.ItemsSource = filteredPlayers;
        }

        private void OnAutoUpdateChanged(object? sender, RoutedEventArgs e)
        {
            if (AutoUpdateCheckBox.IsChecked == true)
            {
                StartAutoUpdate();
            }
            else
            {
                StopAutoUpdate();
            }
        }

        private void OnRefreshClick(object? sender, RoutedEventArgs e)
        {
            LoadPlayers();
            Logger.Instance.Info("Đã làm mới danh sách live players");
        }

        private void OnPlayerSelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            _selectedPlayer = PlayersDataGrid.SelectedItem as Players;
            var hasSelection = _selectedPlayer != null;

            KickPlayerButton.IsEnabled = hasSelection;
            SendMessageButton.IsEnabled = hasSelection;
            TeleportToPlayerButton.IsEnabled = hasSelection;
            ViewPlayerDetailsButton.IsEnabled = hasSelection;
        }

        private void OnKickPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Đã kick player: {_selectedPlayer.CharacterName}");
                // TODO: Implement kick logic
            }
        }

        private void OnSendMessageClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Gửi tin nhắn đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement send message dialog
            }
        }

        private void OnTeleportToPlayerClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Teleport đến player: {_selectedPlayer.CharacterName}");
                // TODO: Implement teleport logic
            }
        }

        private void OnViewPlayerDetailsClick(object? sender, RoutedEventArgs e)
        {
            if (_selectedPlayer != null)
            {
                Logger.Instance.Info($"Xem chi tiết player: {_selectedPlayer.CharacterName}");
                // TODO: Implement player details dialog
            }
        }

        private void OnExportPlayersClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Xuất danh sách live players");
            // TODO: Implement export functionality
        }

        private void OnBroadcastMessageClick(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Info("Gửi thông báo toàn server");
            // TODO: Implement broadcast message dialog
        }

        protected override void OnDetachedFromVisualTree(Avalonia.VisualTreeAttachmentEventArgs e)
        {
            StopAutoUpdate();
            base.OnDetachedFromVisualTree(e);
        }
    }
}
