using System;
using Avalonia.Controls;
using HeroYulgang.Core;
using HeroYulgang.Services;

namespace HeroYulgang.Views;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();

        Console.WriteLine("DEBUG: Bắt đầu khởi tạo MainWindow");

        // Log application start
        Logger.Instance.Info("Ứng dụng đã khởi động");
        Logger.Instance.Debug("Chế độ Debug đã được bật");

        // Test LogViewer with different log levels
        Logger.Instance.Warning("⚠️ Test warning log for LogViewer");
        Logger.Instance.Error("❌ Test error log for LogViewer");
        Logger.Instance.Fatal("💀 Test fatal log for LogViewer");

        // Tải cấu hình
        Console.WriteLine("DEBUG: Bắt đầu tải cấu hình");
        var config = ConfigManager.Instance;
        Logger.Instance.Info($"Đã tải cấu hình máy chủ: {config.ServerSettings.ServerName}");
        Logger.Instance.Debug($"Cổng máy chủ: {config.ServerSettings.GameServerPort}");
        Logger.Instance.Debug($"Số lượng người chơi tối đa: {config.ServerSettings.MaximumOnline}");

        // Khoi tao Database manager
        Console.WriteLine("DEBUG: Bắt đầu khởi tạo DatabaseManager");
        //DatabaseManager.Instance.Initialize();
        Console.WriteLine("DEBUG: Hoàn thành khởi tạo DatabaseManager");

        Logger.Instance.Info("Giao diện tab đã được khởi tạo thành công");
        Console.WriteLine("DEBUG: Hoàn thành khởi tạo MainWindow");
    }
}
