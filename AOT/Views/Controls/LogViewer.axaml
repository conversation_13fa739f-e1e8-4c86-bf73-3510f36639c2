<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:services="using:HeroYulgang.Services"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:CompileBindings="True"
             x:Class="HeroYulgang.Views.Controls.LogViewer">
  <Grid RowDefinitions="Auto,*,Auto">
    <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="5" Margin="5">
      <TextBlock Text="Mức Log:" VerticalAlignment="Center"/>
      <ComboBox x:Name="LogLevelFilter" SelectedIndex="0" Width="120">
        <ComboBoxItem Content="Tất cả"/>
        <ComboBoxItem Content="Debug"/>
        <ComboBoxItem Content="Thông tin"/>
        <ComboBoxItem Content="Cảnh báo"/>
        <ComboBoxItem Content="Lỗi"/>
        <ComboBoxItem Content="Nghiêm trọng"/>
      </ComboBox>
      <Button x:Name="ClearButton" Content="Xóa Log" Margin="10,0,0,0"/>
      <Button x:Name="OpenLogFolderButton" Content="Mở thư mục Log" Margin="10,0,0,0"/>
    </StackPanel>

    <ListBox Grid.Row="1" x:Name="LogListBox" Background="Black" Margin="5">
      <ListBox.ItemTemplate>
        <DataTemplate DataType="services:LogEntry">
          <TextBlock Text="{Binding FormattedMessage}" Foreground="{Binding Color}"
                     FontFamily="Consolas" TextWrapping="Wrap"/>
        </DataTemplate>
      </ListBox.ItemTemplate>
    </ListBox>

    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="5">
      <TextBlock Text="Tổng số log:" VerticalAlignment="Center"/>
      <TextBlock x:Name="LogCountText" Text="0" Margin="5,0,0,0" VerticalAlignment="Center"/>
    </StackPanel>
  </Grid>
</UserControl>
