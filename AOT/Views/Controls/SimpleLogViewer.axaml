<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="HeroYulgang.Views.Controls.SimpleLogViewer">
  <Grid RowDefinitions="Auto,*,Auto">
    <StackPanel Grid.Row="0" Orientation="Horizontal" Spacing="5" Margin="5">
      <Button x:Name="RefreshButton" Content="Refresh Logs"/>
      <Button x:Name="ClearButton" Content="Clear Logs"/>
      <TextBlock x:Name="LogCountText" Text="0 logs" Margin="10,0,0,0" VerticalAlignment="Center"/>
    </StackPanel>

    <ScrollViewer Grid.Row="1" Margin="5">
      <TextBlock x:Name="LogTextBlock" 
                 Background="Black" 
                 Foreground="White" 
                 FontFamily="Consolas" 
                 TextWrapping="Wrap"
                 Padding="5"/>
    </ScrollViewer>

    <TextBlock Grid.Row="2" Text="Simple LogViewer for AOT compatibility" 
               Margin="5" FontSize="10" Opacity="0.7"/>
  </Grid>
</UserControl>
