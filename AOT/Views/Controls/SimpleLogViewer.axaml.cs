using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using HeroYulgang.Services;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace HeroYulgang.Views.Controls
{
    public partial class SimpleLogViewer : UserControl
    {
        private readonly Button? _refreshButton;
        private readonly Button? _clearButton;
        private readonly TextBlock? _logTextBlock;
        private readonly TextBlock? _logCountText;

        public SimpleLogViewer()
        {
            InitializeComponent();

            _refreshButton = this.FindControl<Button>("RefreshButton");
            _clearButton = this.FindControl<Button>("ClearButton");
            _logTextBlock = this.FindControl<TextBlock>("LogTextBlock");
            _logCountText = this.FindControl<TextBlock>("LogCountText");

            if (_refreshButton != null)
                _refreshButton.Click += RefreshButton_Click;

            if (_clearButton != null)
                _clearButton.Click += ClearButton_Click;

            // Initial load
            RefreshLogs();
            
            // Auto-refresh every 2 seconds
            StartAutoRefresh();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void RefreshButton_Click(object? sender, RoutedEventArgs e)
        {
            RefreshLogs();
        }

        private void ClearButton_Click(object? sender, RoutedEventArgs e)
        {
            Logger.Instance.Clear();
            RefreshLogs();
        }

        private void RefreshLogs()
        {
            try
            {
                var logger = Logger.Instance;
                var logs = logger.Logs.ToList(); // Create snapshot to avoid collection modification

                var sb = new StringBuilder();
                foreach (var log in logs.TakeLast(100)) // Show last 100 logs
                {
                    var color = log.Level switch
                    {
                        HeroYulgang.Helpers.LogLevel.Debug => "Gray",
                        HeroYulgang.Helpers.LogLevel.Info => "LightGreen",
                        HeroYulgang.Helpers.LogLevel.Warning => "Orange",
                        HeroYulgang.Helpers.LogLevel.Error => "Red",
                        HeroYulgang.Helpers.LogLevel.Fatal => "DarkRed",
                        _ => "White"
                    };

                    sb.AppendLine($"[{log.Timestamp:HH:mm:ss}] [{log.LevelName}] {log.Message}");
                }

                if (_logTextBlock != null)
                {
                    _logTextBlock.Text = sb.ToString();
                }

                if (_logCountText != null)
                {
                    _logCountText.Text = $"{logs.Count} logs";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error refreshing logs: {ex.Message}");
                if (_logTextBlock != null)
                {
                    _logTextBlock.Text = $"Error loading logs: {ex.Message}";
                }
            }
        }

        private void StartAutoRefresh()
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(2000); // Refresh every 2 seconds
                    
                    try
                    {
                        // Use Dispatcher if available, otherwise just refresh directly
                        if (Avalonia.Threading.Dispatcher.UIThread != null)
                        {
                            Avalonia.Threading.Dispatcher.UIThread.Post(RefreshLogs);
                        }
                        else
                        {
                            RefreshLogs();
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Auto-refresh error: {ex.Message}");
                    }
                }
            });
        }
    }
}
