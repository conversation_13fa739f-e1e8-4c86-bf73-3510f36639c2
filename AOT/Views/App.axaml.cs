using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using HeroYulgang.Services;
using HeroYulgang.Core;
using RxjhServer;
using System.Threading.Tasks;
using System;
using System.Net.Sockets;
using System.Net;

namespace HeroYulgang.Views;

public partial class App : Application
{
    public override void Initialize()
    {
        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // Tạo MainWindow với title động
            var mainWindow = new MainWindow();
            desktop.MainWindow = mainWindow;

            // Cập nhật title với thông tin server
            UpdateWindowTitle(mainWindow);

            // Tự động start World
            _ = AutoStartWorldAsync();

            // Đảm bảo SeriLog được đóng đúng cách khi ứng dụng tắt
            desktop.ShutdownRequested += (sender, e) =>
            {
                Logger.CloseAndFlush();
            };
        }

        base.OnFrameworkInitializationCompleted();
    }

    private void UpdateWindowTitle(MainWindow mainWindow)
    {
        try
        {
            var config = ConfigManager.Instance;
            var serverName = config.ServerSettings.ServerName;
            var port = config.ServerSettings.GameServerPort;
            var localIP = GetLocalIPAddress();

            mainWindow.Title = $"{serverName} - {localIP}:{port}";
            Logger.Instance.Info($"Window title updated: {mainWindow.Title}");
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi cập nhật title: {ex.Message}");
            // Fallback to default title
            mainWindow.Title = "YulgangHero Server Manager";
        }
    }

    private async Task AutoStartWorldAsync()
    {
        try
        {
            Logger.Instance.Info("Đang tự động khởi động World...");

            // Đợi một chút để UI được khởi tạo hoàn toàn
            await Task.Delay(2000);

            var world = World.Instance;

            // Khởi tạo heavy components trước
            await world.InitializeHeavyComponentsAsync();

            // Start World
            bool success = await world.StartAsync();

            if (success)
            {
                Logger.Instance.Info("✓ World đã được tự động khởi động thành công");
            }
            else
            {
                Logger.Instance.Error("✗ Không thể tự động khởi động World");
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Error($"Lỗi khi tự động khởi động World: {ex.Message}");
        }
    }

    private string GetLocalIPAddress()
    {
        string localIP = "127.0.0.1";
        try
        {
            // Lấy địa chỉ IP của máy chủ
            using Socket socket = new(AddressFamily.InterNetwork, SocketType.Dgram, 0);
            socket.Connect("*******", 65530);
            if (socket.LocalEndPoint is IPEndPoint endPoint)
            {
                localIP = endPoint.Address.ToString();
            }
        }
        catch (Exception ex)
        {
            Logger.Instance.Warning($"Không thể lấy địa chỉ IP: {ex.Message}. Sử dụng 127.0.0.1");
        }
        return localIP;
    }
}