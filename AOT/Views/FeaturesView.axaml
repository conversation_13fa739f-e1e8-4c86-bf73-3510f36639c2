<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d" d:DesignWidth="900" d:DesignHeight="600"
             x:Class="HeroYulgang.Views.FeaturesView"
             x:CompileBindings="False">

    <ScrollViewer>
        <StackPanel Margin="16" Spacing="16">
            <!-- Header -->
            <TextBlock Text="Tính năng Server" Classes="header"/>

            <!-- Auto Features Section -->
            <Border Classes="panel" Background="Black">
                <StackPanel Spacing="12">
                    <TextBlock Text="Tính năng tự động" Classes="subheader"/>
                    
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto" Margin="0,8,0,0" Background="Black">
                        <!-- Row 1 -->
                        <Button Grid.Row="0" Grid.Column="0" x:Name="AutoOfflineButton" 
                                Content="Auto Offline" Width="150" Height="40" Classes="primary" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="1" x:Name="AutoPartyButton" 
                                Content="Auto Party" Width="150" Height="40" Classes="primary" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="AutoTradeButton" 
                                Content="Auto Trade" Width="150" Height="40" Classes="primary" Margin="5"/>
                        
                        <!-- Row 2 -->
                        <Button Grid.Row="1" Grid.Column="0" x:Name="AutoPickupButton" 
                                Content="Auto Pickup" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="1" x:Name="AutoHealButton" 
                                Content="Auto Heal" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="AutoBuffButton" 
                                Content="Auto Buff" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        
                        <!-- Row 3 -->
                        <Button Grid.Row="2" Grid.Column="0" x:Name="AutoReviveButton" 
                                Content="Auto Revive" Width="150" Height="40" Classes="warning" Margin="5"/>
                        <Button Grid.Row="2" Grid.Column="1" x:Name="AutoRepairButton" 
                                Content="Auto Repair" Width="150" Height="40" Classes="warning" Margin="5"/>
                        <Button Grid.Row="2" Grid.Column="2" x:Name="AutoSellButton" 
                                Content="Auto Sell" Width="150" Height="40" Classes="warning" Margin="5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Server Management Section -->
            <Border Classes="panel" Background="Black">
                <StackPanel Spacing="12">
                    <TextBlock Text="Quản lý Server" Classes="subheader"/>
                    
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0" Background="Black">
                        <!-- Row 1 -->
                        <Button Grid.Row="0" Grid.Column="0" x:Name="ReloadTemplateButton" 
                                Content="Reload Template" Width="150" Height="40" Classes="success" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="1" x:Name="ReloadConfigButton" 
                                Content="Reload Config" Width="150" Height="40" Classes="success" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="ClearCacheButton" 
                                Content="Clear Cache" Width="150" Height="40" Classes="success" Margin="5"/>
                        
                        <!-- Row 2 -->
                        <Button Grid.Row="1" Grid.Column="0" x:Name="BackupDatabaseButton" 
                                Content="Backup Database" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="1" x:Name="OptimizeDatabaseButton" 
                                Content="Optimize DB" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="CheckDatabaseButton" 
                                Content="Check DB" Width="150" Height="40" Classes="secondary" Margin="5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Event Management Section -->
            <Border Classes="panel" Background="Black">
                <StackPanel Spacing="12">
                    <TextBlock Text="Quản lý Event" Classes="subheader"/>
                    
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0" Background="Black">
                        <!-- Row 1 -->
                        <Button Grid.Row="0" Grid.Column="0" x:Name="StartEventButton" 
                                Content="Start Event" Width="150" Height="40" Classes="success" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="1" x:Name="StopEventButton" 
                                Content="Stop Event" Width="150" Height="40" Classes="danger" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="EventStatusButton" 
                                Content="Event Status" Width="150" Height="40" Classes="primary" Margin="5"/>
                        
                        <!-- Row 2 -->
                        <Button Grid.Row="1" Grid.Column="0" x:Name="SpawnBossButton" 
                                Content="Spawn Boss" Width="150" Height="40" Classes="warning" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="1" x:Name="ClearMonstersButton" 
                                Content="Clear Monsters" Width="150" Height="40" Classes="danger" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="ResetMapsButton" 
                                Content="Reset Maps" Width="150" Height="40" Classes="warning" Margin="5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Player Management Section -->
            <Border Classes="panel" Background="Black">
                <StackPanel Spacing="12">
                    <TextBlock Text="Quản lý Player" Classes="subheader"/>
                    
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0" Background="Black">
                        <!-- Row 1 -->
                        <Button Grid.Row="0" Grid.Column="0" x:Name="KickAllPlayersButton" 
                                Content="Kick All Players" Width="150" Height="40" Classes="danger" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="1" x:Name="SendGlobalMessageButton" 
                                Content="Global Message" Width="150" Height="40" Classes="primary" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="GiveItemAllButton" 
                                Content="Give Item All" Width="150" Height="40" Classes="success" Margin="5"/>
                        
                        <!-- Row 2 -->
                        <Button Grid.Row="1" Grid.Column="0" x:Name="ResetPlayerStatsButton" 
                                Content="Reset Stats" Width="150" Height="40" Classes="warning" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="1" x:Name="TeleportAllButton" 
                                Content="Teleport All" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="BanPlayerButton" 
                                Content="Ban Player" Width="150" Height="40" Classes="danger" Margin="5"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- System Tools Section -->
            <Border Classes="panel" Background="Black">
                <StackPanel Spacing="12" >
                    <TextBlock Text="Công cụ hệ thống" Classes="subheader"/>
                    
                    <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto" Margin="0,8,0,0" Background="Black">
                        <!-- Row 1 -->
                        <Button Grid.Row="0" Grid.Column="0" x:Name="SystemInfoButton" 
                                Content="System Info" Width="150" Height="40" Classes="primary" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="1" x:Name="MemoryUsageButton" 
                                Content="Memory Usage" Width="150" Height="40" Classes="primary" Margin="5"/>
                        <Button Grid.Row="0" Grid.Column="2" x:Name="NetworkStatsButton" 
                                Content="Network Stats" Width="150" Height="40" Classes="primary" Margin="5"/>
                        
                        <!-- Row 2 -->
                        <Button Grid.Row="1" Grid.Column="0" x:Name="ExportLogsButton" 
                                Content="Export Logs" Width="150" Height="40" Classes="secondary" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="1" x:Name="DebugModeButton" 
                                Content="Debug Mode" Width="150" Height="40" Classes="warning" Margin="5"/>
                        <Button Grid.Row="1" Grid.Column="2" x:Name="RestartServiceButton" 
                                Content="Restart Service" Width="150" Height="40" Classes="danger" Margin="5"/>
                    </Grid>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
