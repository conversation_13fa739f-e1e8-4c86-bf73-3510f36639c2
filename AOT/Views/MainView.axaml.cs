using System;
using System.IO;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Interactivity;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.Database;
using System.Diagnostics;
using System.Threading;
using System.Linq;
using Avalonia.Threading;

namespace HeroYulgang.Views
{
    public partial class MainView : UserControl, IDisposable
    {
        private World? _world;
        private bool _isWorldInitialized = false;
        private DispatcherTimer? _statusUpdateTimer;
        private bool _disposed = false;

        public MainView()
        {
            InitializeComponent();
            SetupEventHandlers();
            UpdateButtonStates();

            // Lấy World instance (đã được tự động khởi tạo trong App.axaml.cs)
            _ = InitializeWorldReferenceAsync();

            // Khởi tạo timer để cập nhật trạng thái nút định kỳ
            SetupStatusUpdateTimer();
        }

        private async Task InitializeWorldReferenceAsync()
        {
            try
            {
                Logger.Instance.Info("Đang lấy tham chiếu World...");

                // Lấy World instance (đã được khởi tạo tự động)
                _world = World.Instance;

                // Đợi một chút để World được khởi tạo hoàn toàn
                int attempts = 0;
                while (_world.State == WorldState.Starting && attempts < 30)
                {
                    await Task.Delay(1000);
                    attempts++;
                }

                _isWorldInitialized = (_world.State == WorldState.Running || _world.State == WorldState.Stopped);

                if (_isWorldInitialized)
                {
                    Logger.Instance.Info("Đã lấy tham chiếu World thành công");
                }
                else
                {
                    Logger.Instance.Warning("World chưa sẵn sàng, có thể cần khởi động thủ công");
                }

                // Cập nhật UI trên UI thread
                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    UpdateButtonStates();
                });
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lấy tham chiếu World: {ex.Message}");

                await Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
                {
                    UpdateButtonStates();
                });
            }
        }

        private void SetupEventHandlers()
        {
            StartServerButton.Click += OnStartServerClick;
            StopServerButton.Click += OnStopServerClick;
            SaveDataButton.Click += OnSaveDataClick;
        }

        private void SetupStatusUpdateTimer()
        {
            // Tạo timer để cập nhật trạng thái nút mỗi 2 giây
            _statusUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _statusUpdateTimer.Tick += OnStatusUpdateTimerTick;
            _statusUpdateTimer.Start();
        }

        private void OnStatusUpdateTimerTick(object? sender, EventArgs e)
        {
            // Cập nhật trạng thái nút dựa trên trạng thái hiện tại của World
            UpdateButtonStates();
        }

        private async void OnStartServerClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo. Vui lòng đợi...");
                return;
            }

            if (_world.State != WorldState.Stopped)
            {
                Logger.Instance.Warning("Server không ở trạng thái dừng. Không thể khởi động.");
                return;
            }

            try
            {
                // Vô hiệu hóa các nút trong khi khởi động
                SetButtonsEnabled(false);
                StartServerButton.Content = "Starting...";

                Logger.Instance.Info("=== BẮT ĐẦU KHỞI ĐỘNG GAME SERVER ===");

                // Thực hiện khởi động hoàn toàn GameServer từ đầu
                Logger.Instance.Info("Đang khởi tạo lại các thành phần nặng của World...");
                await _world.InitializeHeavyComponentsAsync();

                Logger.Instance.Info("Đang khởi động World và các Actor...");
                bool success = await _world.StartAsync();

                if (success)
                {
                    Logger.Instance.Info("✓ Game Server đã khởi động thành công");
                    Logger.Instance.Debug("Đang lắng nghe kết nối từ người chơi");
                    Logger.Instance.Info("Máy chủ đã sẵn sàng phục vụ người chơi");
                }
                else
                {
                    Logger.Instance.Error("✗ Không thể khởi động Game Server");
                }

                Logger.Instance.Info("=== HOÀN THÀNH KHỞI ĐỘNG GAME SERVER ===");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động Game Server: {ex.Message}");
            }
            finally
            {
                // Khôi phục nút
                StartServerButton.Content = "Start";
                // Cập nhật trạng thái nút
                UpdateButtonStates();
            }
        }

        private async void OnStopServerClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo.");
                return;
            }

            if (_world.State != WorldState.Running)
            {
                Logger.Instance.Warning("Server không đang chạy.");
                return;
            }

            try
            {
                // Vô hiệu hóa các nút trong khi dừng
                SetButtonsEnabled(false);
                StopServerButton.Content = "Stopping...";

                Logger.Instance.Info("=== BẮT ĐẦU DỪNG GAME SERVER ===");

                // Bước 1: Logout tất cả người chơi đang online
                await LogoutAllPlayersAsync();

                // Bước 2: Dừng World và các Actor
                Logger.Instance.Info("Đang dừng World và các Actor...");
                bool success = await _world.StopAsync();

                if (success)
                {
                    Logger.Instance.Info("✓ Game Server đã dừng thành công");
                }
                else
                {
                    Logger.Instance.Error("✗ Có lỗi khi dừng Game Server");
                }

                Logger.Instance.Info("=== HOÀN THÀNH DỪNG GAME SERVER ===");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng Game Server: {ex.Message}");
            }
            finally
            {
                // Khôi phục nút
                StopServerButton.Content = "Stop";
                // Cập nhật trạng thái nút
                UpdateButtonStates();
            }
        }

        private async Task LogoutAllPlayersAsync()
        {
            try
            {
                Logger.Instance.Info("Đang logout tất cả người chơi online...");

                var stopwatch = Stopwatch.StartNew();
                int totalPlayers = 0;
                int loggedOutPlayers = 0;
                int failedPlayers = 0;

                // Lấy danh sách tất cả người chơi đang kết nối
                var connectedPlayers = World.allConnectedChars.Values.ToList();
                totalPlayers = connectedPlayers.Count;

                Logger.Instance.Info($"Tìm thấy {totalPlayers} người chơi đang online");

                if (totalPlayers == 0)
                {
                    Logger.Instance.Info("Không có người chơi nào đang online");
                    return;
                }

                // Logout từng người chơi
                foreach (var player in connectedPlayers)
                {
                    try
                    {
                        // Gọi method Logout của từng player
                        await Task.Run(() => player.Logout());
                        loggedOutPlayers++;
                        Logger.Instance.Debug($"✓ Đã logout người chơi: {player.CharacterName}");
                    }
                    catch (Exception ex)
                    {
                        failedPlayers++;
                        Logger.Instance.Error($"✗ Lỗi khi logout người chơi {player.CharacterName}: {ex.Message}");
                    }
                }

                stopwatch.Stop();

                // Báo cáo kết quả
                Logger.Instance.Info("=== KẾT QUẢ LOGOUT TẤT CẢ NGƯỜI CHƠI ===");
                Logger.Instance.Info($"- Tổng thời gian: {stopwatch.ElapsedMilliseconds}ms ({stopwatch.Elapsed.TotalSeconds:F2}s)");
                Logger.Instance.Info($"- Tổng số người chơi: {totalPlayers}");
                Logger.Instance.Info($"- Logout thành công: {loggedOutPlayers}");
                Logger.Instance.Info($"- Logout thất bại: {failedPlayers}");
                Logger.Instance.Info($"- Tỷ lệ thành công: {loggedOutPlayers * 100.0 / totalPlayers:F1}%");

                if (loggedOutPlayers == totalPlayers)
                {
                    Logger.Instance.Info("✓ Đã logout thành công tất cả người chơi!");
                }
                else
                {
                    Logger.Instance.Warning($"⚠️ Có {failedPlayers} người chơi không thể logout");
                }

                // Đợi một chút để đảm bảo tất cả kết nối được đóng
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong quá trình logout tất cả người chơi: {ex.Message}");
            }
        }



        private void UpdateButtonStates()
        {
            if (_world == null)
            {
                // World chưa được khởi tạo
                StartServerButton.IsEnabled = false;
                StopServerButton.IsEnabled = false;
                SaveDataButton.IsEnabled = false;
                return;
            }

            switch (_world.State)
            {
                case WorldState.Stopped:
                    StartServerButton.IsEnabled = _isWorldInitialized;
                    StopServerButton.IsEnabled = false;
                    SaveDataButton.IsEnabled = false; // Không thể save data khi server dừng
                    break;

                case WorldState.Running:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = true;
                    SaveDataButton.IsEnabled = true; // Có thể save data khi server đang chạy
                    break;

                case WorldState.Starting:
                case WorldState.Stopping:
                    StartServerButton.IsEnabled = false;
                    StopServerButton.IsEnabled = false;
                    SaveDataButton.IsEnabled = false; // Không save data khi server đang chuyển trạng thái
                    break;
            }
        }

        private void SetButtonsEnabled(bool enabled)
        {
            StartServerButton.IsEnabled = enabled;
            StopServerButton.IsEnabled = enabled;
            SaveDataButton.IsEnabled = enabled;
        }

        private async void OnSaveDataClick(object? sender, RoutedEventArgs e)
        {
            if (_world == null)
            {
                Logger.Instance.Warning("World chưa được khởi tạo.");
                return;
            }

            if (_world.State != WorldState.Running)
            {
                Logger.Instance.Warning("Chỉ có thể lưu dữ liệu khi server đang chạy.");
                return;
            }

            try
            {
                // Vô hiệu hóa button trong khi lưu dữ liệu
                SaveDataButton.IsEnabled = false;
                SaveDataButton.Content = "Saving...";

                Logger.Instance.Info("=== BẮT ĐẦU LƯU DỮ LIỆU TẤT CẢ NGƯỜI CHƠI ===");

                var stopwatch = Stopwatch.StartNew();
                int totalPlayers = 0;
                int savedPlayers = 0;
                int failedPlayers = 0;

                // Lấy danh sách tất cả người chơi đang kết nối
                var connectedPlayers = World.allConnectedChars.Values.ToList();
                totalPlayers = connectedPlayers.Count;

                Logger.Instance.Info($"Tìm thấy {totalPlayers} người chơi đang online");

                if (totalPlayers == 0)
                {
                    Logger.Instance.Info("Không có người chơi nào đang online để lưu dữ liệu");
                    return;
                }

                // Lưu dữ liệu từng người chơi
                foreach (var player in connectedPlayers)
                {
                    try
                    {
                        // Gọi method SaveCharacterData của từng player
                        await player.SaveCharacterDataAsync();
                        savedPlayers++;
                        Logger.Instance.Debug($"✓ Đã lưu dữ liệu cho người chơi: {player.CharacterName}");
                    }
                    catch (Exception ex)
                    {
                        failedPlayers++;
                        Logger.Instance.Error($"✗ Lỗi khi lưu dữ liệu cho người chơi {player.CharacterName}: {ex.Message}");
                    }
                }

                stopwatch.Stop();

                // Báo cáo kết quả
                Logger.Instance.Info("=== KẾT QUẢ LƯU DỮ LIỆU ===");
                Logger.Instance.Info($"- Tổng thời gian: {stopwatch.ElapsedMilliseconds}ms ({stopwatch.Elapsed.TotalSeconds:F2}s)");
                Logger.Instance.Info($"- Tổng số người chơi: {totalPlayers}");
                Logger.Instance.Info($"- Lưu thành công: {savedPlayers}");
                Logger.Instance.Info($"- Lưu thất bại: {failedPlayers}");
                Logger.Instance.Info($"- Tỷ lệ thành công: {(savedPlayers * 100.0 / totalPlayers):F1}%");

                if (savedPlayers == totalPlayers)
                {
                    Logger.Instance.Info("✓ Đã lưu thành công dữ liệu tất cả người chơi!");
                }
                else
                {
                    Logger.Instance.Warning($"⚠️ Có {failedPlayers} người chơi không thể lưu dữ liệu");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi trong quá trình lưu dữ liệu: {ex.Message}");
            }
            finally
            {
                // Khôi phục button
                SaveDataButton.IsEnabled = true;
                SaveDataButton.Content = "Save Data";
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _statusUpdateTimer?.Stop();
                _statusUpdateTimer = null;
                _disposed = true;
            }
        }
    }
}
