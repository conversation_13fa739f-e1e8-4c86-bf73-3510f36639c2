using System;
using System.Net;
using Akka.Actor;
using HeroYulgang.Core.Actors;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;
using RxjhServer.Network;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Lớp quản lý việc chuyển đổi từ NetState sang ActorNetState
    /// </summary>
    public static class PlayerNetworkManager
    {
        /// <summary>
        /// Tạo một ActorNetState mới từ thông tin kết nối
        /// </summary>
        /// <param name="connection">Tham chiếu đến actor kết nối</param>
        /// <param name="sessionId">ID phiên kết nối</param>
        /// <param name="remoteEndPoint">Địa chỉ IP và port của client</param>
        /// <returns>Đối tượng ActorNetState mới</returns>
        public static ActorNetState CreateActorNetState(IActorRef connection, int sessionId, IPEndPoint remoteEndPoint)
        {
            try
            {
                var actorNetState = new ActorNetState(connection, sessionId, remoteEndPoint);
                LogHelper.WriteLine(LogLevel.Debug, $"Đã tạo ActorNetState mới cho phiên {sessionId} từ {remoteEndPoint}");
                return actorNetState;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi tạo ActorNetState: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Cập nhật property Client của Players để sử dụng ActorNetState
        /// </summary>
        /// <param name="player">Đối tượng Players cần cập nhật</param>
        /// <param name="actorNetState">Đối tượng ActorNetState mới</param>
        public static void UpdatePlayerClient(Players player, ActorNetState actorNetState)
        {
            try
            {
                // Gọi phương thức MigrateToActorNetState để cập nhật Client
                player.MigrateToActorNetState(actorNetState);

                LogHelper.WriteLine(LogLevel.Debug, $"Đã cập nhật Client của người chơi {player.CharacterName} sang ActorNetState");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi cập nhật Client của Players: {ex.Message}");
                throw;
            }
        }


    }
}
