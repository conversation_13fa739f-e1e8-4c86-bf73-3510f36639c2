using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// JSON source generator context for PacketBatch configuration
    /// </summary>
    [JsonSourceGenerationOptions(WriteIndented = true)]
    [JsonSerializable(typeof(PacketBatchConfig.BatchSettings))]
    [JsonSerializable(typeof(PacketBatchConfig.BatchMapSettings))]
    [JsonSerializable(typeof(Dictionary<int, bool>))]
    [JsonSerializable(typeof(Dictionary<int, PacketBatchConfig.BatchMapSettings>))]
    public partial class PacketBatchJsonContext : JsonSerializerContext
    {
    }
}
