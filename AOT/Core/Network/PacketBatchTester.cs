using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using HeroYulgang.Utils;
using RxjhServer;

namespace HeroYulgang.Core.Network
{
    /// <summary>
    /// Testing and performance monitoring for packet batching system
    /// </summary>
    public static class PacketBatchTester
    {
        /// <summary>
        /// Performance metrics for batch testing
        /// </summary>
        public class BatchMetrics
        {
            public int TotalPacketsSent { get; set; }
            public int BatchedPackets { get; set; }
            public int IndividualPackets { get; set; }
            public long TotalBytesOriginal { get; set; }
            public long TotalBytesCompressed { get; set; }
            public double CompressionRatio => TotalBytesOriginal > 0 ? (double)TotalBytesCompressed / TotalBytesOriginal : 1.0;
            public double BatchingRatio => TotalPacketsSent > 0 ? (double)BatchedPackets / TotalPacketsSent : 0.0;
            public TimeSpan TestDuration { get; set; }
            public double PacketsPerSecond => TestDuration.TotalSeconds > 0 ? TotalPacketsSent / TestDuration.TotalSeconds : 0;
        }

        private static readonly Dictionary<int, BatchMetrics> _sessionMetrics = new();
        private static readonly object _metricsLock = new object();

        /// <summary>
        /// Record packet sending metrics
        /// </summary>
        public static void RecordPacketSent(int sessionId, bool wasBatched, int originalSize, int compressedSize = 0)
        {
            lock (_metricsLock)
            {
                if (!_sessionMetrics.ContainsKey(sessionId))
                {
                    _sessionMetrics[sessionId] = new BatchMetrics();
                }

                var metrics = _sessionMetrics[sessionId];
                metrics.TotalPacketsSent++;
                metrics.TotalBytesOriginal += originalSize;

                if (wasBatched)
                {
                    metrics.BatchedPackets++;
                    metrics.TotalBytesCompressed += compressedSize > 0 ? compressedSize : originalSize;
                }
                else
                {
                    metrics.IndividualPackets++;
                    metrics.TotalBytesCompressed += originalSize;
                }
            }
        }

        /// <summary>
        /// Get metrics for specific session
        /// </summary>
        public static BatchMetrics GetMetrics(int sessionId)
        {
            lock (_metricsLock)
            {
                return _sessionMetrics.TryGetValue(sessionId, out var metrics) ? metrics : new BatchMetrics();
            }
        }

        /// <summary>
        /// Get aggregated metrics for all sessions
        /// </summary>
        public static BatchMetrics GetAggregatedMetrics()
        {
            lock (_metricsLock)
            {
                var aggregated = new BatchMetrics();
                foreach (var metrics in _sessionMetrics.Values)
                {
                    aggregated.TotalPacketsSent += metrics.TotalPacketsSent;
                    aggregated.BatchedPackets += metrics.BatchedPackets;
                    aggregated.IndividualPackets += metrics.IndividualPackets;
                    aggregated.TotalBytesOriginal += metrics.TotalBytesOriginal;
                    aggregated.TotalBytesCompressed += metrics.TotalBytesCompressed;
                }
                return aggregated;
            }
        }

        /// <summary>
        /// Clear metrics for specific session
        /// </summary>
        public static void ClearMetrics(int sessionId)
        {
            lock (_metricsLock)
            {
                _sessionMetrics.Remove(sessionId);
            }
        }

        /// <summary>
        /// Clear all metrics
        /// </summary>
        public static void ClearAllMetrics()
        {
            lock (_metricsLock)
            {
                _sessionMetrics.Clear();
            }
        }

        /// <summary>
        /// Run performance test comparing batched vs non-batched sending
        /// </summary>
        public static async Task<(BatchMetrics batched, BatchMetrics individual)> RunPerformanceTest(
            int numPackets = 1000, 
            int numPlayers = 10,
            bool enableLogging = true)
        {
            if (enableLogging)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Starting packet batch performance test: {numPackets} packets, {numPlayers} players");
            }

            var batchedMetrics = new BatchMetrics();
            var individualMetrics = new BatchMetrics();

            // Create test movement packets
            var testPackets = CreateTestMovementPackets(numPackets);

            // Test 1: Batched sending
            var batchedStopwatch = Stopwatch.StartNew();
            await TestBatchedSending(testPackets, numPlayers, batchedMetrics);
            batchedStopwatch.Stop();
            batchedMetrics.TestDuration = batchedStopwatch.Elapsed;

            // Wait a bit between tests
            await Task.Delay(1000);

            // Test 2: Individual sending
            var individualStopwatch = Stopwatch.StartNew();
            await TestIndividualSending(testPackets, numPlayers, individualMetrics);
            individualStopwatch.Stop();
            individualMetrics.TestDuration = individualStopwatch.Elapsed;

            if (enableLogging)
            {
                LogHelper.WriteLine(LogLevel.Info, "Performance test completed:");
                LogHelper.WriteLine(LogLevel.Info, $"Batched: {batchedMetrics.PacketsPerSecond:F2} packets/sec, {batchedMetrics.CompressionRatio:F2} compression ratio");
                LogHelper.WriteLine(LogLevel.Info, $"Individual: {individualMetrics.PacketsPerSecond:F2} packets/sec");
                LogHelper.WriteLine(LogLevel.Info, $"Performance improvement: {(batchedMetrics.PacketsPerSecond / individualMetrics.PacketsPerSecond):F2}x");
            }

            return (batchedMetrics, individualMetrics);
        }

        /// <summary>
        /// Create test movement packets
        /// </summary>
        private static List<SendingClass> CreateTestMovementPackets(int count)
        {
            var packets = new List<SendingClass>();
            var random = new Random();

            for (int i = 0; i < count; i++)
            {
                var packet = new SendingClass();
                
                // Simulate movement packet data (similar to NPC movement)
                packet.Write(random.Next(1000, 5000)); // X position
                packet.Write(random.Next(1000, 5000)); // Y position
                packet.Write(15.0f); // Z position
                packet.Write4(-1); // Unknown field
                packet.Write4(random.Next(0, 3)); // Movement type
                packet.Write(random.Next(10, 50)); // Speed
                packet.Write4(random.Next(1000, 10000)); // HP
                
                // Additional fields for newer version
                if (World.Item_Db_Byte_Length >= 15.0)
                {
                    packet.Write(random.Next(1000, 5000)); // Old X
                    packet.Write(15.0f); // Old Z
                    packet.Write(random.Next(1000, 5000)); // Old Y
                    packet.Write4(0);
                    packet.Write2(0);
                }

                packets.Add(packet);
            }

            return packets;
        }

        /// <summary>
        /// Test batched packet sending
        /// </summary>
        private static async Task TestBatchedSending(List<SendingClass> packets, int numPlayers, BatchMetrics metrics)
        {
            // Simulate batched sending by grouping packets
            var batchSize = PacketBatchConfig.Settings.MaxBatchSize;
            var batches = packets.Select((packet, index) => new { packet, index })
                                .GroupBy(x => x.index / batchSize)
                                .Select(g => g.Select(x => x.packet).ToArray())
                                .ToList();

            foreach (var batch in batches)
            {
                // Simulate compression
                var originalSize = batch.Sum(p => p.Length);
                var compressedSize = (int)(originalSize * 0.7); // Assume 30% compression

                metrics.TotalPacketsSent += batch.Length;
                metrics.BatchedPackets += batch.Length;
                metrics.TotalBytesOriginal += originalSize;
                metrics.TotalBytesCompressed += compressedSize;

                // Simulate network delay
                await Task.Delay(1);
            }
        }

        /// <summary>
        /// Test individual packet sending
        /// </summary>
        private static async Task TestIndividualSending(List<SendingClass> packets, int numPlayers, BatchMetrics metrics)
        {
            foreach (var packet in packets)
            {
                var packetSize = packet.Length;

                metrics.TotalPacketsSent++;
                metrics.IndividualPackets++;
                metrics.TotalBytesOriginal += packetSize;
                metrics.TotalBytesCompressed += packetSize;

                // Simulate network delay (individual packets have more overhead)
                await Task.Delay(1);
            }
        }

        /// <summary>
        /// Generate performance report
        /// </summary>
        public static string GeneratePerformanceReport()
        {
            var aggregated = GetAggregatedMetrics();
            var report = $@"
=== Packet Batching Performance Report ===
Total Packets Sent: {aggregated.TotalPacketsSent:N0}
Batched Packets: {aggregated.BatchedPackets:N0} ({aggregated.BatchingRatio:P1})
Individual Packets: {aggregated.IndividualPackets:N0}
Original Data Size: {aggregated.TotalBytesOriginal:N0} bytes
Compressed Data Size: {aggregated.TotalBytesCompressed:N0} bytes
Compression Ratio: {aggregated.CompressionRatio:F3}
Bandwidth Savings: {(1 - aggregated.CompressionRatio):P1}
==========================================";

            return report;
        }

        /// <summary>
        /// Monitor batch performance in real-time
        /// </summary>
        public static void StartPerformanceMonitoring(int intervalSeconds = 60)
        {
            Task.Run(async () =>
            {
                while (true)
                {
                    await Task.Delay(intervalSeconds * 1000);
                    
                    var report = GeneratePerformanceReport();
                    LogHelper.WriteLine(LogLevel.Info, report);
                    
                    // Clear metrics after reporting to avoid memory buildup
                    ClearAllMetrics();
                }
            });
        }
    }
}
