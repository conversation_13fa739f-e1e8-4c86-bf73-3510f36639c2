using System;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// <PERSON><PERSON>c loại gói tin trong hệ thống
    /// </summary>
    public enum PacketType : ushort
    {
        // Các loại gói tin
        Login = 1,
        Valid1375 = 1375,
    }

    /// <summary>
    /// Đ<PERSON>i diện cho một gói tin trong hệ thống
    /// </summary>
    public class Packet(PacketType type, byte[] data)
    {
        public PacketType Type { get; } = type;
        public byte[] Data { get; } = data;
        public int Length { get; } = data.Length;

        public static Packet Parse(byte[] buffer, int length)
        {
            // Giả sử cấu trúc gói tin: [2 byte loại gói tin][2 byte độ dài][dữ liệu]
            if (length < 4)
            {
                throw new ArgumentException("Gói tin không hợp lệ: quá ngắn");
            }

            ushort type = BitConverter.ToUInt16(buffer, 8);
            ushort dataLength = BitConverter.ToUInt16(buffer, 10);

            if (length < 4 + dataLength)
            {
                throw new ArgumentException("<PERSON><PERSON>i tin không hợp lệ: độ dài không khớp");
            }

            // byte[] data = new byte[dataLength];
            // Array.Copy(buffer, 4, data, 0, dataLength);
            // Keep the original buffer to complie with the old 
            return new Packet((PacketType)type, buffer);
        }

        public byte[] ToByteArray()
        {
            byte[] result = new byte[4 + Length];
            BitConverter.GetBytes((ushort)Type).CopyTo(result, 0);
            BitConverter.GetBytes((ushort)Length).CopyTo(result, 2);
            Data.CopyTo(result, 4);
            return result;
        }
    }
}
