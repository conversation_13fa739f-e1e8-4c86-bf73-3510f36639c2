using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Threading.Tasks;
using Akka.Actor;
using HeroYulgang.Core.Actors;
using HeroYulgang.Core.Network;
using HeroYulgang.Services;
using HeroYulgang.Helpers;
using RxjhServer;
using RxjhServer.HelperTools;
using Akka.IO;
using System.Linq;
using RxjhServer.Database;
using System.Collections.Concurrent;
using HeroYulgang.Database.FreeSql; // Thêm cho hàng đợi ưu tiên

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor xử lý các gói tin - đã hợp nhất với PlayerPacketHandlerActor
    /// </summary>
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
    public class PacketHandlerActor : ReceiveActor
    {
        private readonly Dictionary<PacketType, Func<IActorRef, ClientSession, Packet, Task>> _handlers = [];

        // Thêm các thuộc tính để xử lý player packets
        private Players _player;
        private ActorNetState _actorNetState;
        private IActorRef _clientConnection;

        private readonly ConcurrentQueue<(ProcessPacket, int)> _priorityQueue = new(); // Hàng đợi ưu tiên

        public PacketHandlerActor()
        {
            // Đăng ký các message handler
            ReceiveAsync<ProcessPacket>(HandlePacket);
            Receive<ProcessPlayerPacket>(HandlePlayerPacket);
            Receive<SetPlayerContext>(SetPlayerContext);
            ReceiveAsync<ProcessQueue>(ProcessQueueAsync);
            RegisterHandlers();
            Self.Tell(new ProcessQueue());
            Logger.Instance.Debug($"Đã tạo PacketHandlerActor mới: {Self.Path}");
        }

        private void RegisterHandlers()
        {
             _handlers[PacketType.Login] = HandleLoginAsync;
        }

        private void EnqueuePacket(ProcessPacket message)
        {
            var packet = Packet.Parse(message.Data, message.Data.Length);
            int priority = packet.Type == PacketType.Login ? 1 : 0; // Ưu tiên Login
            _priorityQueue.Enqueue((message, priority));
        }
        public class ProcessQueue { }
        private async Task ProcessQueueAsync(ProcessQueue _)
        {
            while (_priorityQueue.TryDequeue(out var item))
            {
                var (message, priority) = item;
                var packet = Packet.Parse(message.Data, message.Data.Length);
                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    await handler(message.Connection, message.Session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"2 Không có handler cho gói tin loại {packet.Type} từ session {message.Session.SessionId}");
                }
            }
            Self.Tell(new ProcessQueue()); // Tiếp tục xử lý hàng đợi
        }


        /// <summary>
        /// Check if The account is exists, create a player object with it account, then process to old method to
        /// validate account with loginserver
        /// </summary>
        /// <param name="connection"></param>
        /// <param name="session"></param>
        /// <param name="packet"></param>
        /// <returns></returns>
        private async Task HandleLoginAsync(IActorRef connection, ClientSession session, Packet packet)
        {
            // Xử lý gói tin Login
            // Giả sử dữ liệu gói tin: [username length][username][password length][password]
            try
            {
                var reader = new BinaryReader(new MemoryStream(packet.Data));
                reader.BaseStream.Seek(12, SeekOrigin.Begin);
                byte[] userNameByte = reader.ReadBytes(14);
                string username = System.Text.Encoding.GetEncoding(World.Language_Charset).GetString(userNameByte).Trim().Replace("\0", String.Empty);
                if (username.Length == 0)
                {
                    //Dispose connection
                    connection.Tell(Tcp.Close.Instance);
                    return;
                }

                var account = await AccountDb.FindAccount(username);
                if (account == null)
                {
                    //Dispose connection
                    connection.Tell(Tcp.Close.Instance);
                    return;
                }
                Logger.Instance.Debug($"Yêu cầu đăng nhập từ {username}");

                // Tạo player với thông tin cơ bản
                var player = new Players
                {
                    AccountID = account.fld_id.ToString(),
                    LanIp = username,
                    SessionID = session.SessionId
                };
                // Xác thực thành công
                session.IsAuthenticated = false;
                session.AccountId = username;

                // Thông báo cho ClientActor về player reference và yêu cầu xử lý đăng nhập
                // ClientActor sẽ tự động thiết lập quan hệ giữa Player và ActorNetState, sau đó xử lý đăng nhập
                var clientActorPath = $"/user/tcpManager/client-{session.SessionId}";
                var clientActor = Context.ActorSelection(clientActorPath);

                // Chuẩn bị data cho đăng nhập
                var dataX = new byte[packet.Data.Length - 2];
                Buffer.BlockCopy(packet.Data, 0, dataX, 0, 6);
                Buffer.BlockCopy(packet.Data, 8, dataX, 6, packet.Data.Length - 8);

                // Gửi message với cả player reference và login data
                clientActor.Tell(new SetPlayerReferenceAndLogin(player, dataX, session, username));
                // Xử lý packet đăng nhập trực tiếp
                //ProcessPlayerPacketData(packet.Data, packet.Data.Length);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin đăng nhập 2: {ex.Message}");

                // Gửi phản hồi thất bại
                byte[] responseData = BitConverter.GetBytes(0); // 0 = thất bại
                var response = new Packet(PacketType.Login, responseData);
                await SendPacketAsync(connection, response.ToByteArray());
            }
        }


        /// <summary>
        /// Thiết lập context cho player packet handling
        /// </summary>
        private void SetPlayerContext(SetPlayerContext message)
        {
            try
            {
                _player = message.Player;
                _actorNetState = message.ActorNetState;
                _clientConnection = message.ClientConnection;

                Logger.Instance.Debug($"Đã thiết lập player context cho {_player.AccountID} (SessionID: {_player.SessionID})");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi thiết lập player context: {ex.Message}");
            }
        }

        /// <summary>
        /// Xử lý ProcessPlayerPacket message
        /// </summary>
        private void HandlePlayerPacket(ProcessPlayerPacket message)
        {
            try
            {
                if (_player == null)
                {
                    Logger.Instance.Warning("Nhận ProcessPlayerPacket nhưng _player là null");
                    return;
                }

                // Kiểm tra _player.Client thay vì _actorNetState
                if (_player.Client == null)
                {
                    Logger.Instance.Warning($"Nhận ProcessPlayerPacket nhưng _player.Client là null cho player {_player.CharacterName}");
                    return;
                }

                if (_clientConnection == null)
                {
                    Logger.Instance.Warning("Nhận ProcessPlayerPacket nhưng _clientConnection là null");
                    return;
                }

                // Cập nhật _actorNetState từ _player.Client
                _actorNetState = _player.Client as ActorNetState;

                //Logger.Instance.Debug($"Đã nhận ProcessPlayerPacket cho {_player.UserName}");

                // Xử lý packet giống như PlayerPacketHandlerActor
                //await Task.Run(() => ProcessPlayerPacketData(message.Data, message.Length));
                ProcessPlayerPacketData(message.Data, message.Length);
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ người chơi {_player?.CharacterName}: {ex.Message}");
            }
        }

        private async Task HandlePacket(ProcessPacket message)
        {
            try
            {
                var packet = Packet.Parse(message.Data, message.Data.Length);

                // Ghi log gói tin đã phân tích
                PacketLogger.LogPacket(message.Session.SessionId, packet, true);

                if (_handlers.TryGetValue(packet.Type, out var handler))
                {
                    await handler(message.Connection, message.Session, packet);
                }
                else
                {
                    Logger.Instance.Warning($"3 Không có handler cho gói tin loại {packet.Type} từ session {message.Session.SessionId}");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý gói tin từ session {message.Session.SessionId}: {ex.Message}");
            }
        }

        // Các phương thức xử lý gói tin

        // Helper method để gửi gói tin
        private Task SendPacketAsync(IActorRef connection, byte[] data)
        {
            try
            {
                // Gửi gói tin đến TcpManagerActor thay vì Context.Parent
                var tcpManager = Context.ActorSelection("/user/tcpManager");
                tcpManager.Tell(new SendPacket(connection, data));

                // Ghi log gói tin gửi đi
                if (data.Length >= 2)
                {
                    // Tìm session ID từ connection
                    tcpManager.Tell(new GetSessionIdFromConnection(connection, sessionId =>
                    {
                        if (sessionId != -1)
                        {
                            PacketLogger.LogOutgoingPacket(sessionId, data);
                        }
                    }));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi packet: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Xử lý packet data cho player - được chuyển từ PlayerPacketHandlerActor
        /// </summary>
        private void ProcessPlayerPacketData(byte[] data, int length)
        {
            if (_player == null)
            {
                Logger.Instance.Warning("ProcessPlayerPacketData: _player là null");
                return;
            }

            // Kiểm tra _player.Client trước khi sử dụng
            if (_player.Client == null)
            {
                Logger.Instance.Warning($"ProcessPlayerPacketData: _player.Client là null cho player {_player.AccountID}");
                return;
            }

            // Lấy packet type từ data
            int packetType = BitConverter.ToInt16(data, 8);

            // Debug logging cho packet type
            // if (World.Debug > 1)
            // {
            //     Logger.Instance.Debug($"[{Self.Path.Name}] Xử lý packet type {packetType} cho player {_player.AccountID}");
            // }

            try
            {
                // cut byte 6-7 from data
                var oldPacket = new byte[length - 2];
                Buffer.BlockCopy(data, 0, oldPacket, 0, 6);
                Buffer.BlockCopy(data,8, oldPacket, 6, length - 8);

                // Kiểm tra xem người chơi đã kết nối chưa
                if (!World.allConnectedChars.TryGetValue(_player.SessionID, out var _))
                {
                    // Người chơi chưa kết nối, xử lý pre-login packets
                    HandlePreLoginPackets(packetType, oldPacket, oldPacket.Length);
                    return;
                }

                // Xử lý heartbeat riêng
                if (packetType == 176)
                {
                    _player.Phat_Hien_Nhip_Tim(oldPacket, oldPacket.Length);
                    return;
                }

                // Kiểm tra kết nối thành công
                if (!_player._connectionSucceeded)
                {
                    Logger.Instance.Warning($"[{Self.Path.Name}] Connection not succeeded cho player {_player.AccountID}, packet type {packetType}");
                    throw new Exception("Connection not succeeded");
                }

                // Xử lý các loại packet khác
                Logger.Instance.Debug($"PacketActor [{Self.Path.Name}] Xử lý game packet {packetType} cho player {_player.AccountID}");
                HandleGamePackets(packetType, oldPacket, oldPacket.Length);
            }
            catch (Exception ex)
            {
                // Kiểm tra _player.Client trước khi truy cập thuộc tính
                string clientInfo = "unknown";
                string worldId = "unknown";

                try
                {
                    if (_player.Client != null)
                    {
                        worldId = _player.Client.SessionID.ToString();
                        clientInfo = _player.Client.ToString();
                    }
                }
                catch (Exception clientEx)
                {
                    Logger.Instance.Error($"Lỗi khi truy cập thông tin client: {clientEx.Message}");
                }

                LogHelper.WriteLine(LogLevel.Error, $"Manage Packet() Lỗi tại case: [{packetType}]-[{worldId}]-[{clientInfo}] [{ex}]");
                Logger.Instance.Error($"Stack trace: {ex.StackTrace}");

                // Kiểm tra trước khi dispose
                try
                {
                    _player.Client?.Dispose();
                }
                catch (Exception disposeEx)
                {
                    Logger.Instance.Error($"Lỗi khi dispose client: {disposeEx.Message}");
                }

                LogHelper.WriteLine(LogLevel.Error,$"Disconnected![{_player.AccountID}]-[{_player.CharacterName}][Mã dis 10]");
            }
        }

        /// <summary>
        /// Xử lý các packet trước khi đăng nhập
        /// </summary>
        private void HandlePreLoginPackets(int packetType, byte[] data, int length)
        {
            switch (packetType)
            {
                case 20:
                    _player.CreateCharacter(data, length).GetAwaiter().GetResult();
                    break;
                case 16:
                    _player.GetAListOfPeople(data, length).GetAwaiter().GetResult();
                    break;
                case 1:
                    _player.KetNoi_DangNhap(data, length);
                    break;
                case 3:
                    _player.DangXuat(data, length);
                    break;
                case 5:
                    _player.CharacterLogin(data, length);
                    break;
                case 143:
                    _player.Display();
                    break;
                case 56:
                    _player.KiemTraNhanVat_CoTonTaiHayKhong(data, length).GetAwaiter().GetResult();
                    break;
                case 30:
                    _player.XoaBoNhanVat(data, length).GetAwaiter().GetResult();
                    break;
                case 836:
                    _player.XacMinhThongTinDangNhapId(data, length);
                    break;
                case 218:
                case 211:
                    _player.ChangeLineVerification(data, length);
                    break;
                case 16666:
                    _player.IsAttackConfirmation(data, length);
                    break;
                case 5638:
                case 8212:
                    _player.VersionVerification(data, length);
                    break;
            }
        }

        /// <summary>
        /// Xử lý các packet trong game
        /// </summary>
        private void HandleGamePackets(int packetType, byte[] data, int length)
        {
            _player.ManagePacket(data, data.Length);
        }
    }

    /// <summary>
    /// Message để thiết lập player context cho PacketHandlerActor
    /// </summary>
    public class SetPlayerContext
    {
        public Players Player { get; }
        public ActorNetState ActorNetState { get; }
        public IActorRef ClientConnection { get; }

        public SetPlayerContext(Players player, ActorNetState actorNetState, IActorRef clientConnection)
        {
            Player = player;
            ActorNetState = actorNetState;
            ClientConnection = clientConnection;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin cho người chơi
    /// </summary>
     public class ProcessPlayerPacket
    {
        public byte[] Data { get; }
        public int Length { get; }

        public ProcessPlayerPacket(byte[] data, int length)
        {
            Data = data;
            Length = length;
        }
    }
}
