using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Net;
using System.Net.Sockets;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Core.Managers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor quản lý kết nối TCP
    /// </summary>
    [DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.NonPublicConstructors)]
    public class TcpManagerActor : ReceiveActor
    {
        private readonly ConfigManager _configManager;
        private readonly Dictionary<IActorRef, ClientSession> _sessions = [];
        private bool _isRunning = false;
        private ICancelable _inactiveConnectionsChecker;

        public TcpManagerActor()
        {
            _configManager = ConfigManager.Instance;

            // Đ<PERSON>nh nghĩa các message handler
            Receive<StartServer>(msg => StartServer());
            Receive<StopServer>(msg => StopServer());
            Receive<Tcp.Bound>(msg => HandleBound(msg));
            Receive<Tcp.Connected>(msg => HandleConnected(msg));
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
            Receive<Tcp.Received>(msg => HandleReceivedDirectly(msg));
            Receive<CheckInactiveConnections>(msg => CheckInactiveConnections());
            Receive<SendPacket>(msg => SendPacket(msg));
            Receive<BroadcastPacket>(msg => BroadcastPacket(msg));
            Receive<CloseConnection>(msg => HandleCloseConnection(msg));
            Receive<GetSessionCount>(msg => Sender.Tell(new SessionCountResponse(_sessions.Count)));
            Receive<GetAllSessions>(msg => Sender.Tell(new AllSessionsResponse(new List<ClientSession>(_sessions.Values))));
            Receive<GetSessionIdFromConnection>(msg => HandleGetSessionIdFromConnection(msg));
        }

        protected override void PreStart()
        {
            // Tự động khởi động server khi actor được tạo
            Self.Tell(new StartServer());
        }

        protected override void PostStop()
        {
            // Dừng server khi actor bị dừng
            StopServer();
        }

        private void StartServer()
        {
            if (_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng đã đang chạy");
                return;
            }

            try
            {
                int port = _configManager.ServerSettings.GameServerPort;

                // Lấy tham chiếu đến Tcp manager của Akka.IO
                var tcpManager = Context.System.Tcp();

                // Bind vào cổng được chỉ định
                tcpManager.Tell(new Tcp.Bind(Self, new IPEndPoint(IPAddress.Any, port)));

                _isRunning = true;
                Logger.Instance.Info($"Máy chủ mạng đã khởi động trên cổng {port}");

                // Bắt đầu task kiểm tra các kết nối không hoạt động
                ScheduleInactiveConnectionsCheck();
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi khởi động máy chủ mạng: {ex.Message}");
            }
        }

        private void StopServer()
        {
            if (!_isRunning)
            {
                Logger.Instance.Warning("Máy chủ mạng không chạy");
                return;
            }

            try
            {
                // Hủy task kiểm tra kết nối không hoạt động
                _inactiveConnectionsChecker?.Cancel();

                // Đóng tất cả các kết nối
                foreach (var session in _sessions)
                {
                    session.Key.Tell(Tcp.Close.Instance);
                }

                _sessions.Clear();
                _isRunning = false;
                Logger.Instance.Info("Máy chủ mạng đã dừng");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi dừng máy chủ mạng: {ex.Message}");
            }
        }

        private void HandleConnected(Tcp.Connected connected)
        {
            // Lấy thông tin về IP và port của server
            int serverPort = _configManager.ServerSettings.GameServerPort;
            string serverIp = GetLocalIPAddress();

            Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} tới máy chủ {serverIp}:{serverPort}");

            // Chấp nhận kết nối
            Sender.Tell(new Tcp.Register(Self));

            // Tạo session mới sử dụng SessionIdManager
            int sessionId = SessionIdManager.Instance.AllocatePlayerSessionId();
            if (sessionId == -1)
            {
                Logger.Instance.Error("Không thể cấp phát SessionID cho client mới - Pool đã hết!");
                Sender.Tell(Tcp.Close.Instance);
                return;
            }

            var clientSession = new ClientSession(sessionId, connected.RemoteAddress, Sender);

            // Lưu session
            _sessions[Sender] = clientSession;

            // Tạo ClientActor duy nhất để xử lý cả kết nối và packet
            Context.ActorOf(Props.Create(() => new ClientActor(Sender, clientSession)), $"client-{sessionId}");

            Logger.Instance.Info($"Client kết nối: {connected.RemoteAddress} (SessionID: {sessionId}) tới máy chủ {serverIp}:{serverPort}");
        }

        private static string GetLocalIPAddress()
        {
            string localIP = "127.0.0.1";
            try
            {
                // Lấy địa chỉ IP của máy chủ
                using Socket socket = new(AddressFamily.InterNetwork, SocketType.Dgram, 0);
                socket.Connect("*******", 65530);
                if (socket.LocalEndPoint is System.Net.IPEndPoint endPoint)
                {
                    localIP = endPoint.Address.ToString();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Warning($"Không thể lấy địa chỉ IP: {ex.Message}. Sử dụng 127.0.0.1");
            }
            return localIP;
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed closed)
        {
            if (_sessions.TryGetValue(Sender, out var session))
            {
                Logger.Instance.Info($"Client ngắt kết nối: {session.RemoteEndPoint} (SessionID: {session.SessionId})");

                // Giải phóng SessionID để tái sử dụng
                SessionIdManager.Instance.ReleaseSessionId(session.SessionId);

                // TODO: Luu data nhan vat
                if (World.allConnectedChars.TryGetValue(session.SessionId, out var player))
                {
                    player.Logout();
                }
                _sessions.Remove(Sender);
            }
        }

        private void ScheduleInactiveConnectionsCheck()
        {
            // Kiểm tra mỗi 30 giây
            // disalbe in development mode to use breakpoint
            if (_configManager.AppSettings.Environment.Equals("Development", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }
            _inactiveConnectionsChecker = Context.System.Scheduler.ScheduleTellRepeatedlyCancelable(
                TimeSpan.FromSeconds(30),
                TimeSpan.FromSeconds(30),
                Self,
                new CheckInactiveConnections(),
                Self);
        }

        private void CheckInactiveConnections()
        {
            try
            {
                int timeoutSeconds = _configManager.ServerSettings.AutomaticConnectionTime;
                var now = DateTime.Now;
                var timeoutTime = now.AddSeconds(-timeoutSeconds);

                // Tìm các session không hoạt động
                var inactiveSessions = new List<KeyValuePair<IActorRef, ClientSession>>();
                foreach (var session in _sessions)
                {
                    if (session.Value.LastActivityTime < timeoutTime)
                    {
                        inactiveSessions.Add(session);
                    }
                }

                // Đóng các session không hoạt động
                foreach (var session in inactiveSessions)
                {
                    // Giải phóng SessionID
                    SessionIdManager.Instance.ReleaseSessionId(session.Value.SessionId);

                    session.Key.Tell(Tcp.Close.Instance);
                    _sessions.Remove(session.Key);
                    Logger.Instance.Info($"Đóng kết nối không hoạt động: {session.Value.RemoteEndPoint} (SessionID: {session.Value.SessionId})");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi kiểm tra kết nối không hoạt động: {ex.Message}");
            }
        }

        private void SendPacket(SendPacket message)
        {
            try
            {
                // Bỏ qua nếu connection là null
                if (message.Connection == null)
                {
                   // Logger.Instance.Warning("SendPacket: Connection là null");
                    return;
                }

                // Bỏ qua nếu client là offline (sử dụng ActorRefs.NoSender)
                if (message.Connection == Akka.Actor.ActorRefs.NoSender)
                {
                    // if (World.Debug > 1)
                    // {
                    //     Logger.Instance.Debug("Bỏ qua gửi packet cho offline client (NoSender)");
                    // }
                    return;
                }

                // Kiểm tra session tồn tại và gửi packet
                if (_sessions.TryGetValue(message.Connection, out var session))
                {
                    message.Connection.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    session.UpdateActivity();
                }
                else
                {
                    if (World.Debug > 1)
                    {
                        Logger.Instance.Warning($"Không tìm thấy session cho connection: {message.Connection.Path}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi gửi gói tin: {ex.Message}");
            }
        }

        private void BroadcastPacket(BroadcastPacket message)
        {
            try
            {
                foreach (var session in _sessions)
                {
                    if (message.Filter == null || message.Filter(session.Value))
                    {
                        session.Key.Tell(Tcp.Write.Create(ByteString.FromBytes(message.Data)));
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi broadcast gói tin: {ex.Message}");
            }
        }

        private void HandleBound(Tcp.Bound bound)
        {
            Logger.Instance.Info($"Máy chủ đã bind thành công vào {bound.LocalAddress}");
        }

        private void HandleReceivedDirectly(Tcp.Received received)
        {
            // Debug logging cho tất cả packet nhận được
            byte[] rawData = received.Data.ToArray();
           // Logger.Instance.Debug($"[TCP DEBUG] HandleReceivedDirectly - Raw packet length: {rawData.Length}");
           // Logger.Instance.Debug($"[TCP DEBUG] Raw packet hex: {BitConverter.ToString(rawData).Replace("-", "")}");

            // Chuyển tiếp tin nhắn đến ClientActor tương ứng
            // Thông thường, tin nhắn này sẽ được gửi trực tiếp đến ClientActor
            // Nhưng chúng ta xử lý nó ở đây để tránh dead letters

            if (_sessions.TryGetValue(Sender, out var session))
            {
               // Logger.Instance.Debug($"[TCP DEBUG] Found session {session.SessionId} for packet");

                // Tìm ClientActor tương ứng
                var clientActorPath = $"/user/tcpManager/client-{session.SessionId}";
                var clientActor = Context.ActorSelection(clientActorPath);

                //Logger.Instance.Debug($"[TCP DEBUG] Forwarding packet to ClientActor: {clientActorPath}");

                // Chuyển tiếp tin nhắn trực tiếp mà không cần kiểm tra resolve
                // ActorSelection sẽ tự động xử lý việc gửi tin nhắn đến actor đích
                // Nếu actor không tồn tại, tin nhắn sẽ được gửi đến DeadLetters
                clientActor.Tell(received);

                // Cập nhật thời gian hoạt động
                session.UpdateActivity();
            }
            else
            {
                Logger.Instance.Warning($"Nhận dữ liệu từ kết nối không xác định: {Sender.Path}");
            }
        }

        private void HandleGetSessionIdFromConnection(GetSessionIdFromConnection message)
        {
            if (_sessions.TryGetValue(message.Connection, out var session))
            {
                message.Callback(session.SessionId);
            }
            else
            {
                message.Callback(-1);
            }
        }

        private void HandleCloseConnection(CloseConnection message)
        {
            try
            {
                if (_sessions.TryGetValue(message.Connection, out var session))
                {
                    Logger.Instance.Info($"Đóng kết nối từ yêu cầu: {session.RemoteEndPoint} (SessionID: {session.SessionId})");

                    // Giải phóng SessionID
                    SessionIdManager.Instance.ReleaseSessionId(session.SessionId);

                    message.Connection.Tell(Tcp.Close.Instance);
                    _sessions.Remove(message.Connection);
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi đóng kết nối: {ex.Message}");
            }
        }
    }

    // Các message classes
    public class StartServer { }
    public class StopServer { }
    public class CheckInactiveConnections { }

    public class SendPacket
    {
        public IActorRef Connection { get; }
        public byte[] Data { get; }

        public SendPacket(IActorRef connection, byte[] data)
        {
            Connection = connection;
            Data = data;
        }
    }

    public class BroadcastPacket
    {
        public byte[] Data { get; }
        public Predicate<ClientSession>? Filter { get; }

        public BroadcastPacket(byte[] data, Predicate<ClientSession>? filter = null)
        {
            Data = data;
            Filter = filter;
        }
    }

    public class CloseConnection
    {
        public IActorRef Connection { get; }

        public CloseConnection(IActorRef connection)
        {
            Connection = connection;
        }
    }

    public class GetSessionCount { }

    public class SessionCountResponse
    {
        public int Count { get; }

        public SessionCountResponse(int count)
        {
            Count = count;
        }
    }

    public class GetAllSessions { }

    public class AllSessionsResponse
    {
        public List<ClientSession> Sessions { get; }

        public AllSessionsResponse(List<ClientSession> sessions)
        {
            Sessions = sessions;
        }
    }

    public class GetSessionIdFromConnection
    {
        public IActorRef Connection { get; }
        public Action<int> Callback { get; }

        public GetSessionIdFromConnection(IActorRef connection, Action<int> callback)
        {
            Connection = connection;
            Callback = callback;
        }
    }
}
