# Script to automatically add AOT attributes to all FreeSql entity classes
param(
    [string]$ProjectRoot = "."
)

Write-Host "🔍 Adding AOT attributes to FreeSql entity classes..." -ForegroundColor Green

# Find all entity classes
$entityFiles = Get-ChildItem -Path "$ProjectRoot\Database\FreeSql\Entities" -Recurse -Filter "*.cs" | 
    Where-Object { $_.Name -notlike "*__*" -and $_.Name -notlike "*.Designer.cs" }

$processedCount = 0

foreach ($file in $entityFiles) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $modified = $false
    
    Write-Host "  📄 Processing: $($file.Name)" -ForegroundColor Cyan
    
    # Check if already has System.Diagnostics.CodeAnalysis using
    if ($content -notmatch "using System\.Diagnostics\.CodeAnalysis;") {
        # Add the using statement after the last using statement
        $content = $content -replace "(using [^;]+;)(\r?\n\r?\nnamespace)", "`$1`r`nusing System.Diagnostics.CodeAnalysis;`$2"
        $modified = $true
        Write-Host "    ✓ Added using System.Diagnostics.CodeAnalysis" -ForegroundColor Yellow
    }
    
    # Check if class already has DynamicallyAccessedMembers attribute
    if ($content -notmatch "\[DynamicallyAccessedMembers") {
        # Find the class declaration and add attribute
        if ($content -match "(\s+)\[JsonObject\([^\]]+\), Table\([^\]]+\)\](\r?\n)(\s+)(public partial class \w+)") {
            $indent = $matches[1]
            $newline = $matches[2]
            $classIndent = $matches[3]
            $classDecl = $matches[5]
            
            $newClassDecl = "$indent[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]$newline$indent[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]$newline$classIndent$classDecl"
            
            $content = $content -replace [regex]::Escape($matches[0]), $newClassDecl
            $modified = $true
            Write-Host "    ✓ Added DynamicallyAccessedMembers attribute" -ForegroundColor Yellow
        }
    }
    
    # Check if class already has parameterless constructor
    if ($content -notmatch "/// <summary>\s*\r?\n\s*/// Parameterless constructor for AOT compatibility") {
        # Find the class opening brace and add constructor
        if ($content -match "(public partial class \w+ \{)(\r?\n)") {
            $classStart = $matches[1]
            $newline = $matches[2]
            
            $constructor = @"
$classStart$newline
		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public $($file.BaseName)()
		{
		}$newline
"@
            
            $content = $content -replace [regex]::Escape($matches[0]), $constructor
            $modified = $true
            Write-Host "    ✓ Added parameterless constructor" -ForegroundColor Yellow
        }
    }
    
    # Write back if modified
    if ($modified) {
        Set-Content -Path $file.FullName -Value $content -Encoding UTF8
        $processedCount++
        Write-Host "    ✅ Updated $($file.Name)" -ForegroundColor Green
    } else {
        Write-Host "    ⏭️  Already up to date: $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "🎉 Processed $processedCount entity files!" -ForegroundColor Green
