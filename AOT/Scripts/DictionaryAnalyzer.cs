using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace Scripts
{
    /// <summary>
    /// Analyzer to scan codebase for Dictionary usage and recommend migrations
    /// Phân tích codebase để tìm Dictionary và đưa ra <PERSON><PERSON><PERSON>ến nghị migration
    /// </summary>
    public class DictionaryAnalyzer
    {
        private readonly string _rootPath;
        private readonly List<DictionaryUsage> _findings = new();

        public DictionaryAnalyzer(string rootPath)
        {
            _rootPath = rootPath;
        }

        public class DictionaryUsage
        {
            public string FilePath { get; set; }
            public int LineNumber { get; set; }
            public string Declaration { get; set; }
            public string VariableName { get; set; }
            public bool IsStatic { get; set; }
            public bool IsPublic { get; set; }
            public bool IsThreadSafeDictionary { get; set; }
            public bool IsConcurrentDictionary { get; set; }
            public RiskLevel Risk { get; set; }
            public string Recommendation { get; set; }
        }

        public enum RiskLevel
        {
            Low,      // Template data, read-only
            Medium,   // Instance dictionaries in async methods
            High,     // Static dictionaries accessed from multiple threads
            Critical  // Known concurrent access issues
        }

        public void AnalyzeCodebase()
        {
            Console.WriteLine("🔍 Scanning codebase for Dictionary usage...");
            
            var csFiles = Directory.GetFiles(_rootPath, "*.cs", SearchOption.AllDirectories)
                .Where(f => !f.Contains("bin") && !f.Contains("obj"))
                .ToList();

            foreach (var file in csFiles)
            {
                AnalyzeFile(file);
            }

            GenerateReport();
        }

        private void AnalyzeFile(string filePath)
        {
            try
            {
                var lines = File.ReadAllLines(filePath);
                
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    
                    // Skip comments and empty lines
                    if (line.StartsWith("//") || string.IsNullOrWhiteSpace(line))
                        continue;

                    AnalyzeLine(filePath, i + 1, line);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error analyzing {filePath}: {ex.Message}");
            }
        }

        private void AnalyzeLine(string filePath, int lineNumber, string line)
        {
            // Patterns to match Dictionary declarations
            var patterns = new[]
            {
                @"Dictionary<([^>]+)>\s+(\w+)",
                @"static\s+Dictionary<([^>]+)>\s+(\w+)",
                @"public\s+static\s+Dictionary<([^>]+)>\s+(\w+)",
                @"ThreadSafeDictionary<([^>]+)>\s+(\w+)",
                @"ConcurrentDictionary<([^>]+)>\s+(\w+)"
            };

            foreach (var pattern in patterns)
            {
                var matches = Regex.Matches(line, pattern);
                foreach (Match match in matches)
                {
                    var usage = new DictionaryUsage
                    {
                        FilePath = filePath,
                        LineNumber = lineNumber,
                        Declaration = line,
                        VariableName = match.Groups[2].Value,
                        IsStatic = line.Contains("static"),
                        IsPublic = line.Contains("public"),
                        IsThreadSafeDictionary = line.Contains("ThreadSafeDictionary"),
                        IsConcurrentDictionary = line.Contains("ConcurrentDictionary")
                    };

                    AssessRisk(usage);
                    _findings.Add(usage);
                }
            }
        }

        private void AssessRisk(DictionaryUsage usage)
        {
            // Already thread-safe
            if (usage.IsThreadSafeDictionary || usage.IsConcurrentDictionary)
            {
                usage.Risk = RiskLevel.Low;
                usage.Recommendation = "✅ Already thread-safe";
                return;
            }

            // Static public dictionaries are high risk
            if (usage.IsStatic && usage.IsPublic)
            {
                usage.Risk = RiskLevel.High;
                usage.Recommendation = "🔴 Convert to ConcurrentDictionary - high concurrency risk";
                return;
            }

            // Static dictionaries are medium-high risk
            if (usage.IsStatic)
            {
                usage.Risk = RiskLevel.Medium;
                usage.Recommendation = "🟡 Consider ConcurrentDictionary if accessed from multiple threads";
                return;
            }

            // Check for known high-risk variables
            var highRiskNames = new[] { "allConnectedChars", "NpcList", "ItemList", "BannedList", "GuildList" };
            if (highRiskNames.Any(name => usage.VariableName.Contains(name)))
            {
                usage.Risk = RiskLevel.Critical;
                usage.Recommendation = "🔴 CRITICAL: Convert to ConcurrentDictionary immediately";
                return;
            }

            // Default to low risk
            usage.Risk = RiskLevel.Low;
            usage.Recommendation = "🟢 Low risk - monitor for async usage";
        }

        private void GenerateReport()
        {
            Console.WriteLine("\n📊 DICTIONARY ANALYSIS REPORT");
            Console.WriteLine(new string('=', 50));

            var groupedByRisk = _findings.GroupBy(f => f.Risk).OrderByDescending(g => g.Key);

            foreach (var group in groupedByRisk)
            {
                Console.WriteLine($"\n{GetRiskIcon(group.Key)} {group.Key.ToString().ToUpper()} RISK ({group.Count()} items):");
                
                foreach (var finding in group.Take(10)) // Show top 10 per category
                {
                    Console.WriteLine($"  📁 {Path.GetFileName(finding.FilePath)}:{finding.LineNumber}");
                    Console.WriteLine($"     Variable: {finding.VariableName}");
                    Console.WriteLine($"     {finding.Recommendation}");
                    Console.WriteLine();
                }

                if (group.Count() > 10)
                {
                    Console.WriteLine($"     ... and {group.Count() - 10} more");
                }
            }

            // Summary
            Console.WriteLine("\n📋 MIGRATION PRIORITY:");
            var critical = _findings.Where(f => f.Risk == RiskLevel.Critical).Count();
            var high = _findings.Where(f => f.Risk == RiskLevel.High).Count();
            var medium = _findings.Where(f => f.Risk == RiskLevel.Medium).Count();

            Console.WriteLine($"🔴 IMMEDIATE ACTION NEEDED: {critical + high} dictionaries");
            Console.WriteLine($"🟡 REVIEW NEEDED: {medium} dictionaries");
            Console.WriteLine($"✅ ALREADY SAFE: {_findings.Where(f => f.IsThreadSafeDictionary || f.IsConcurrentDictionary).Count()} dictionaries");
        }

        private string GetRiskIcon(RiskLevel risk)
        {
            return risk switch
            {
                RiskLevel.Critical => "🚨",
                RiskLevel.High => "🔴",
                RiskLevel.Medium => "🟡",
                RiskLevel.Low => "🟢",
                _ => "❓"
            };
        }

        public static void RunAnalysis(string[] args)
        {
            var rootPath = args.Length > 0 ? args[0] : @"c:\Users\<USER>\Desktop\HEROYG\AOT";
            
            var analyzer = new DictionaryAnalyzer(rootPath);
            analyzer.AnalyzeCodebase();
            
            Console.WriteLine("\n💡 NEXT STEPS:");
            Console.WriteLine("1. Migrate CRITICAL and HIGH risk dictionaries first");
            Console.WriteLine("2. Use DictionaryMigrationHelper for safe migration");
            Console.WriteLine("3. Test thoroughly after each migration");
            Console.WriteLine("4. Monitor performance impact");
        }
    }
}
