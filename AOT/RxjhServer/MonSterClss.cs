namespace RxjhServer;

public class MonSterClss
{
	private string string_0;

	private int int_0;

	private int int_1;

	private int int_2;

	private double double_0;

	private double double_1;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private int int_9;

	private int int_10;

	private int int_11;

	public string fld_name
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int fld_pid
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int fld_level
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int fld_hp
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public double fld_at
	{
		get
		{
			return double_0;
		}
		set
		{
			double_0 = value;
		}
	}

	public double fld_df
	{
		get
		{
			return double_1;
		}
		set
		{
			double_1 = value;
		}
	}

	public int fld_exp
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int fld_auto
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int fld_boss
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int fld_npc
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int fld_quest
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int fld_questid
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int fld_stages
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public int fld_questitem
	{
		get
		{
			return int_10;
		}
		set
		{
			int_10 = value;
		}
	}

	public int fld_pp
	{
		get
		{
			return int_11;
		}
		set
		{
			int_11 = value;
		}
	}
	public int FLD_GOLD { get; set; }

    public int FLD_Accuracy { get; set; }

    public int FLD_Evasion { get; set; }

    public int FLD_FreeDrop { get; set; }
}
