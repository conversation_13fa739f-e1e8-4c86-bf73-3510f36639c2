using System;
using HeroYulgang.Utils;

namespace RxjhServer
{
    /// <summary>
    /// Lớp mở rộng kết nối Players với MarketPlace
    /// </summary>
    public partial class Players
    {
        private MarketPlace _marketPlace;

        /// <summary>
        /// <PERSON><PERSON> thống chợ
        /// </summary>
        public MarketPlace MarketPlaceSystem => _marketPlace ??= new MarketPlace(this);

        #region MarketPlace Forwarding Methods

        /// <summary>
        /// Xử lý các yêu cầu từ chợ
        /// </summary>
        public void MarketPlace(byte[] data)
        {
            MarketPlaceSystem.MarketPlaceRequest(data);
        }

        #endregion
    }
}