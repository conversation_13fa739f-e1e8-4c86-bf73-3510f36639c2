using System;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    public partial class Players
    {
        // Biến lưu trữ người chơi đã giết
        private Players _lastAttacker;

        private Players[] _contributors;

        /// <summary>
        /// L<PERSON><PERSON> thông tin người chơi đã tấn công
        /// </summary>
        /// <param name="attacker">Ngư<PERSON>i chơi tấn công</param>
        public void SetLastAttacker(Players attacker)
        {
            if (attacker != null && attacker != this)
            {
                _lastAttacker = attacker;
            }
        }

        public void AddContributors(Players contributor)
        {
            if (contributor != null && contributor != this)
            {
                Array.Resize(ref _contributors, _contributors.Length + 1);
                _contributors[_contributors.Length - 1] = contributor;
            }
        }

        /// <summary>
        /// Hook vào phương thức Death để xử lý sự kiện khi người chơi chết
        /// </summary>
        public void HookDeath()
        {
            try
            {
                // G<PERSON>i sự kiện khi người chơi chết
                PlayerEvents.OnPlayerDeath(this, _lastAttacker, _contributors);

                // Đặt lại biến _lastAttacker
                _lastAttacker = null;
                _contributors = Array.Empty<Players>();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi xử lý hook Death: {ex.Message}");
            }
        }
    }
}
