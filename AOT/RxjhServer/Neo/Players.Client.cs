using System;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.Network;

namespace RxjhServer
{
    public partial class Players
    {
        public long ExtremeArenaPoints { get; internal set; }
        public bool ExtremeArenaIsDeath { get; internal set; }
        public void ExtremeArenaSetRewards(int result, long totalDamage, int killCount, int deathCount, int participantCount, ref int points, ref int stampCount)
        {
            throw new NotImplementedException();
        }

        public void ExtremeArenaCentralRevive()
        {
            throw new NotImplementedException();
        }

        public void ExtremeArenaReviveInPlace()
        {
            throw new NotImplementedException();
        }
        /// <summary>
        /// <PERSON>yển đổi từ NetState sang ActorNetState
        /// </summary>
        /// <param name="actorNetState">Đ<PERSON><PERSON> tượng ActorNetState mới</param>
        public void MigrateToActorNetState(ActorNetState actorNetState)
        {
            try
            {
                // Cập nhật tham chiế<PERSON> từ ActorNetState đến Players
                actorNetState.Player = this;

                // Cập nhật property Client
                Client = actorNetState;

                LogHelper.WriteLine(LogLevel.Info, $"Đã chuyển đổi thành công người chơi {CharacterName} sang ActorNetState");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi chuyển đổi sang ActorNetState: {ex.Message}");
                throw;
            }
        }
    }
}
