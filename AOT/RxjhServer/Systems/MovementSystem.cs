using System;
using HeroYulgang.Helpers;

namespace RxjhServer.Systems
{
    /// <summary>
    /// Advanced movement system configuration and utilities
    /// </summary>
    public static class MovementSystem
    {
        /// <summary>
        /// Configuration for movement validation system
        /// </summary>
        public static class Config
        {
            // Speed validation settings
            public static float SpeedTolerance = 1.15f;        // 15% tolerance for network lag
            public static float PositionTolerance = 1.25f;     // 25% tolerance for position validation
            public static int MaxViolationsPerMinute = 8;      // Max violations before action
            public static int ViolationResetTime = 30000;      // 30 seconds reset time
            
            // Position validation settings
            public static float MinValidDistance = 0.1f;       // Minimum distance to consider as movement
            public static float MaxTeleportDistance = 200f;    // Max distance before considering teleport
            public static float MaxPositionDrift = 50f;        // Max allowed position drift from server
            
            // System toggles
            public static bool EnablePositionCorrection = true;
            public static bool EnableSpeedLogging = true;
            public static bool EnableTeleportDetection = true;
            public static bool EnableAdvancedValidation = true;
            
            // Performance settings
            public static int ValidationCacheSize = 1000;      // Cache size for validation data
            public static int MaxValidationHistory = 10;       // Max movement history to keep
            public static float ValidationUpdateInterval = 100f; // Milliseconds between validations
            
            /// <summary>
            /// Load configuration from World settings
            /// </summary>
            public static void LoadFromWorld()
            {
                try
                {
                    // Load from World.DiDong_TocDo and other settings if available
                    if (World.DiDong_TocDo != null && World.DiDong_TocDo.Length > 0)
                    {
                        // Configuration can be loaded from database or config files
                        LogHelper.WriteLine(LogLevel.Info, "Movement system configuration loaded from World settings");
                    }
                    
                    // Apply violation settings from World
                    if (World.SoLan_VuotQuaChoPhep_Trong30Giay > 0)
                    {
                        MaxViolationsPerMinute = World.SoLan_VuotQuaChoPhep_Trong30Giay;
                    }
                    
                    LogHelper.WriteLine(LogLevel.Info, $"Movement system initialized - Speed tolerance: {SpeedTolerance}, Max violations: {MaxViolationsPerMinute}");
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error loading movement system configuration: {ex.Message}");
                }
            }
            
            /// <summary>
            /// Update configuration at runtime
            /// </summary>
            public static void UpdateConfig(string setting, float value)
            {
                try
                {
                    switch (setting.ToLower())
                    {
                        case "speedtolerance":
                            SpeedTolerance = Math.Max(1.0f, Math.Min(2.0f, value));
                            break;
                        case "positiontolerance":
                            PositionTolerance = Math.Max(1.0f, Math.Min(3.0f, value));
                            break;
                        case "maxteleportdistance":
                            MaxTeleportDistance = Math.Max(50f, Math.Min(1000f, value));
                            break;
                        case "maxviolations":
                            MaxViolationsPerMinute = (int)Math.Max(1, Math.Min(50, value));
                            break;
                        default:
                            LogHelper.WriteLine(LogLevel.Warning, $"Unknown movement config setting: {setting}");
                            break;
                    }
                    
                    LogHelper.WriteLine(LogLevel.Info, $"Movement config updated: {setting} = {value}");
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error updating movement config: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// Movement statistics and monitoring
        /// </summary>
        public static class Statistics
        {
            public static long TotalMovements = 0;
            public static long SpeedViolations = 0;
            public static long PositionViolations = 0;
            public static long TeleportDetections = 0;
            public static long PositionCorrections = 0;
            public static DateTime LastReset = DateTime.Now;
            
            /// <summary>
            /// Record a movement event
            /// </summary>
            public static void RecordMovement()
            {
                TotalMovements++;
            }
            
            /// <summary>
            /// Record a speed violation
            /// </summary>
            public static void RecordSpeedViolation()
            {
                SpeedViolations++;
            }
            
            /// <summary>
            /// Record a position violation
            /// </summary>
            public static void RecordPositionViolation()
            {
                PositionViolations++;
            }
            
            /// <summary>
            /// Record a teleport detection
            /// </summary>
            public static void RecordTeleportDetection()
            {
                TeleportDetections++;
            }
            
            /// <summary>
            /// Record a position correction
            /// </summary>
            public static void RecordPositionCorrection()
            {
                PositionCorrections++;
            }
            
            /// <summary>
            /// Get violation rate per hour
            /// </summary>
            public static double GetViolationRate()
            {
                var hours = DateTime.Now.Subtract(LastReset).TotalHours;
                if (hours <= 0) return 0;
                
                return (SpeedViolations + PositionViolations + TeleportDetections) / hours;
            }
            
            /// <summary>
            /// Reset statistics
            /// </summary>
            public static void Reset()
            {
                TotalMovements = 0;
                SpeedViolations = 0;
                PositionViolations = 0;
                TeleportDetections = 0;
                PositionCorrections = 0;
                LastReset = DateTime.Now;
                
                LogHelper.WriteLine(LogLevel.Info, "Movement statistics reset");
            }
            
            /// <summary>
            /// Get statistics summary
            /// </summary>
            public static string GetSummary()
            {
                var uptime = DateTime.Now.Subtract(LastReset);
                var violationRate = GetViolationRate();
                
                return $"Movement Stats - Uptime: {uptime:hh\\:mm\\:ss}, " +
                       $"Total: {TotalMovements}, Speed Violations: {SpeedViolations}, " +
                       $"Position Violations: {PositionViolations}, Teleports: {TeleportDetections}, " +
                       $"Corrections: {PositionCorrections}, Rate: {violationRate:F2}/hr";
            }
        }
        
        /// <summary>
        /// Initialize the movement system
        /// </summary>
        public static void Initialize()
        {
            try
            {
                Config.LoadFromWorld();
                Statistics.Reset();
                
                LogHelper.WriteLine(LogLevel.Info, "Advanced Movement System initialized successfully");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Failed to initialize Movement System: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Calculate maximum allowed speed for a player
        /// </summary>
        public static float CalculateMaxSpeed(Players player)
        {
            try
            {
                if (player == null) return 100f;
                
                // Use the player's calculated max speed with tolerance
                return player.TocDoDiChuyen_Max * 2 * Config.SpeedTolerance;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating max speed: {ex.Message}");
                return 100f; // Default fallback speed
            }
        }
        
        /// <summary>
        /// Validate if a movement is reasonable
        /// </summary>
        public static bool IsMovementReasonable(float distance, float timeMs, float maxSpeed)
        {
            if (timeMs <= 0) return true; // Skip validation for zero time
            if (distance < Config.MinValidDistance) return true; // Skip micro-movements
            
            var actualSpeed = (distance * 1000f) / timeMs; // pixels per second
            return actualSpeed <= maxSpeed;
        }
        
        /// <summary>
        /// Check if distance indicates teleportation
        /// </summary>
        public static bool IsTeleportDistance(float distance)
        {
            return distance > Config.MaxTeleportDistance;
        }
    }
}
