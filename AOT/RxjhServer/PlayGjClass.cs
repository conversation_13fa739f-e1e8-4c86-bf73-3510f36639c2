namespace RxjhServer;

public class PlayGjClass
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private string Hostteam_;

	public string HostTeam
	{
		get
		{
			return Hostteam_;
		}
		set
		{
			Hostteam_ = value;
		}
	}

	public int TeamID
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int PlayID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int Gjsl
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int Gjxl
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}
}
