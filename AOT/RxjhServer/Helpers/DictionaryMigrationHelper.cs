using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.Helpers
{
    /// <summary>
    /// Helper class for migrating Dictionary to ConcurrentDictionary
    /// Hỗ trợ migration từ Dictionary sang ConcurrentDictionary
    /// </summary>
    public static class DictionaryMigrationHelper
    {
        /// <summary>
        /// Convert Dictionary to ConcurrentDictionary safely
        /// Chuyển đổi Dictionary sang ConcurrentDictionary an toàn
        /// </summary>
        public static ConcurrentDictionary<TKey, TValue> ToConcurrentDictionary<TKey, TValue>(
            this Dictionary<TKey, TValue> dictionary) where TK<PERSON> : notnull
        {
            if (dictionary == null)
                return new ConcurrentDictionary<TKey, TValue>();

            return new ConcurrentDictionary<TKey, TValue>(dictionary);
        }

        /// <summary>
        /// Safely migrate existing Dictionary to ConcurrentDictionary with data preservation
        /// Migration an toàn với bảo toàn dữ liệu
        /// </summary>
        public static ConcurrentDictionary<TK<PERSON>, TValue> MigrateSafely<TK<PERSON>, TValue>(
            Dictionary<TKey, TValue> source, 
            object lockObject = null) where TK<PERSON> : notnull
        {
            if (source == null)
                return new ConcurrentDictionary<TKey, TValue>();

            var lockObj = lockObject ?? new object();
            
            lock (lockObj)
            {
                try
                {
                    // Create snapshot to avoid modification during enumeration
                    var snapshot = new Dictionary<TKey, TValue>(source);
                    return new ConcurrentDictionary<TKey, TValue>(snapshot);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Migration failed: {ex.Message}");
                    return new ConcurrentDictionary<TKey, TValue>();
                }
            }
        }

        /// <summary>
        /// Check if Dictionary needs thread-safe replacement
        /// Kiểm tra xem Dictionary có cần thay thế thread-safe không
        /// </summary>
        public static bool NeedsThreadSafeReplacement<TKey, TValue>(
            Dictionary<TKey, TValue> dictionary,
            bool isStaticField = false,
            bool isAccessedFromMultipleThreads = false,
            bool isUsedInAsyncContext = false)
        {
            // Static fields accessed from multiple threads definitely need thread-safety
            if (isStaticField && (isAccessedFromMultipleThreads || isUsedInAsyncContext))
                return true;

            // Any dictionary used in async context should be thread-safe
            if (isUsedInAsyncContext)
                return true;

            return false;
        }

        /// <summary>
        /// Performance comparison between Dictionary and ConcurrentDictionary
        /// So sánh hiệu suất giữa Dictionary và ConcurrentDictionary
        /// </summary>
        public static void LogPerformanceImpact(string dictionaryName, int itemCount)
        {
            LogHelper.WriteLine(LogLevel.Info, 
                $"Migration {dictionaryName}: {itemCount} items. " +
                $"ConcurrentDictionary có overhead nhỏ (~10-20%) nhưng thread-safe");
        }

        /// <summary>
        /// Validate migration success
        /// Xác thực migration thành công
        /// </summary>
        public static bool ValidateMigration<TKey, TValue>(
            Dictionary<TKey, TValue> original,
            ConcurrentDictionary<TKey, TValue> migrated) where TKey : notnull
        {
            try
            {
                if (original == null && migrated == null)
                    return true;

                if (original == null || migrated == null)
                    return false;

                if (original.Count != migrated.Count)
                {
                    LogHelper.WriteLine(LogLevel.Warning, 
                        $"Count mismatch: Original={original.Count}, Migrated={migrated.Count}");
                    return false;
                }

                // Check if all keys exist
                foreach (var key in original.Keys)
                {
                    if (!migrated.ContainsKey(key))
                    {
                        LogHelper.WriteLine(LogLevel.Error, $"Missing key after migration: {key}");
                        return false;
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, "Migration validation successful");
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Migration validation failed: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Extension methods for easier Dictionary operations
    /// Extension methods để thao tác Dictionary dễ dàng hơn
    /// </summary>
    public static class DictionaryExtensions
    {
        /// <summary>
        /// Thread-safe add or update for ConcurrentDictionary
        /// Thêm hoặc cập nhật thread-safe cho ConcurrentDictionary
        /// </summary>
        public static void AddOrUpdate<TKey, TValue>(
            this ConcurrentDictionary<TKey, TValue> dictionary,
            TKey key,
            TValue value) where TKey : notnull
        {
            dictionary.AddOrUpdate(key, value, (k, v) => value);
        }

        /// <summary>
        /// Safe remove with logging
        /// Xóa an toàn với logging
        /// </summary>
        public static bool SafeRemove<TKey, TValue>(
            this ConcurrentDictionary<TKey, TValue> dictionary,
            TKey key,
            string context = "") where TKey : notnull
        {
            try
            {
                var result = dictionary.TryRemove(key, out _);
                if (!result && !string.IsNullOrEmpty(context))
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Failed to remove key {key} in {context}");
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing key {key}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get value with default fallback
        /// Lấy giá trị với fallback mặc định
        /// </summary>
        public static TValue GetValueOrDefault<TKey, TValue>(
            this ConcurrentDictionary<TKey, TValue> dictionary,
            TKey key,
            TValue defaultValue = default) where TKey : notnull
        {
            return dictionary.TryGetValue(key, out var value) ? value : defaultValue;
        }
    }
}
