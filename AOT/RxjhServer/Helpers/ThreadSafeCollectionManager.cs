using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using HeroYulgang.Helpers;

namespace RxjhServer.Helpers
{
    /// <summary>
    /// Thread-safe collection manager để xử lý concurrent access issues
    /// </summary>
    public static class ThreadSafeCollectionManager
    {
        private static readonly object _lockObject = new object();
        
        /// <summary>
        /// Safely add item to collection with duplicate key handling
        /// </summary>
        public static bool SafeAdd<TKey, TValue>(IDictionary<TKey, TValue> collection, TKey key, TValue value, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    if (!collection.ContainsKey(key))
                    {
                        collection.Add(key, value);
                        return true;
                    }
                    else
                    {
                        // Key already exists, update instead
                        collection[key] = value;
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeAdd failed for key {key}: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Safely remove item from collection
        /// </summary>
        public static bool SafeRemove<TKey, TValue>(IDictionary<TKey, TValue> collection, TK<PERSON> key, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return collection.Remove(key);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeRemove failed for key {key}: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Safely get value from collection
        /// </summary>
        public static TValue SafeGet<TKey, TValue>(IDictionary<TKey, TValue> collection, TKey key, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return collection.TryGetValue(key, out var value) ? value : default(TValue);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeGet failed for key {key}: {ex.Message}");
                    return default(TValue);
                }
            }
        }

        /// <summary>
        /// Safely iterate over collection values
        /// </summary>
        public static List<TValue> SafeGetValues<TKey, TValue>(IDictionary<TKey, TValue> collection, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return new List<TValue>(collection.Values);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeGetValues failed: {ex.Message}");
                    return new List<TValue>();
                }
            }
        }

        /// <summary>
        /// Safely check if key exists
        /// </summary>
        public static bool SafeContainsKey<TKey, TValue>(IDictionary<TKey, TValue> collection, TKey key, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return collection.ContainsKey(key);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeContainsKey failed for key {key}: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// Safely clear collection
        /// </summary>
        public static void SafeClear<TKey, TValue>(IDictionary<TKey, TValue> collection, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    collection.Clear();
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeClear failed: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Safely get collection count
        /// </summary>
        public static int SafeCount<TKey, TValue>(IDictionary<TKey, TValue> collection, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return collection.Count;
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeCount failed: {ex.Message}");
                    return 0;
                }
            }
        }

        /// <summary>
        /// Safely perform batch operations on collection
        /// </summary>
        public static void SafeBatchOperation<TKey, TValue>(IDictionary<TKey, TValue> collection, 
            Action<IDictionary<TKey, TValue>> operation, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    operation(collection);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"SafeBatchOperation failed: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Create a thread-safe snapshot of collection for iteration
        /// </summary>
        public static Dictionary<TKey, TValue> CreateSnapshot<TKey, TValue>(IDictionary<TKey, TValue> collection, object lockObject = null)
        {
            var lockObj = lockObject ?? _lockObject;
            
            lock (lockObj)
            {
                try
                {
                    return new Dictionary<TKey, TValue>(collection);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"CreateSnapshot failed: {ex.Message}");
                    return new Dictionary<TKey, TValue>();
                }
            }
        }
    }
}
