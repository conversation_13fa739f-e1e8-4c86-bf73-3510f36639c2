using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Centralized timer management for all NPCs to reduce memory usage and improve performance
    /// Replaces individual timers per NPC with a single shared timer system
    /// </summary>
    public static class NPCTimerManager
    {
        /// <summary>
        /// Global timer that processes all NPC behavior updates
        /// Checks every 100ms but NPCs are processed based on their individual behavior schedules
        /// </summary>
        private static readonly System.Timers.Timer _globalUpdateTimer = new(100); // 100ms interval
        
        /// <summary>
        /// Dictionary storing update information for each NPC
        /// </summary>
        private static readonly ConcurrentDictionary<int, NPCUpdateInfo> _npcUpdates = new();
        
        /// <summary>
        /// Flag to track if the timer manager is initialized
        /// </summary>
        private static bool _isInitialized = false;
        
        /// <summary>
        /// Lock object for initialization
        /// </summary>
        private static readonly object _initLock = new object();
        
        /// <summary>
        /// Initialize the timer manager (called automatically when first NPC is registered)
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;
            
            lock (_initLock)
            {
                if (_isInitialized) return;
                
                _globalUpdateTimer.Elapsed += ProcessAllNPCUpdates;
                _globalUpdateTimer.AutoReset = true;
                _globalUpdateTimer.Enabled = true;
                _isInitialized = true;
                
                LogHelper.WriteLine(LogLevel.Info, "NPCTimerManager initialized successfully");
            }
        }

        /// <summary>
        /// Register an NPC for timer-based updates
        /// </summary>
        /// <param name="npc">NPC to register</param>
        /// <param name="moveInterval">Movement update interval in milliseconds</param>
        public static void RegisterNPC(NpcClass npc, int moveInterval = 5000)
        {
            if (!_isInitialized)
            {
                Initialize();
            }

            try
            {
                var now = DateTime.Now;
                var updateInfo = new NPCUpdateInfo
                {
                    NPC = npc,
                    MoveInterval = moveInterval,
                    NextMoveTime = now.AddMilliseconds(RNG.Next(2000, moveInterval)),
                    NextAttackTime = now.AddMilliseconds(RNG.Next(500, 1500)),
                    NextRespawnTime = DateTime.MaxValue,
                    RespawnInterval = npc.FLD_NEWTIME * 1000,
                    LastUpdateTime = now
                };
                _npcUpdates.AddOrUpdate(npc.NPC_SessionID, updateInfo, (key, existing) => updateInfo);
                //LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} (ID: {npc.FLD_PID}, SessionID: {npc.NPC_SessionID}) registered. Total: {_npcUpdates.Count}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error registering NPC {npc.Name} (ID: {npc.FLD_PID}, SessionID: {npc.NPC_SessionID}): {ex.Message}");
            }
        }

        /// <summary>
        /// Unregister an NPC from timer updates
        /// </summary>
        /// <param name="npcSessionID">NPC session ID to unregister</param>
        public static void UnregisterNPC(int npcSessionID)
        {
            _npcUpdates.TryRemove(npcSessionID, out _);

            // Clean up behavior manager tracking data
            NPCBehaviorManager.CleanupNPC(npcSessionID);

            LogHelper.WriteLine(LogLevel.Info, $"NPC {npcSessionID} unregistered from timer updates. Total: {_npcUpdates.Count}");
        }
        
        /// <summary>
        /// Update move interval for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="newInterval">New move interval in milliseconds</param>
        public static void UpdateMoveInterval(int npcSessionID, int newInterval)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                updateInfo.MoveInterval = newInterval;
            }
        }

        /// <summary>
        /// Enable/disable movement for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable movement, false to disable</param>
        public static void SetMovementEnabled(int npcSessionID, bool enabled)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    // Reset next move time to enable movement
                    updateInfo.NextMoveTime = DateTime.Now.AddMilliseconds(RNG.Next(1000, updateInfo.MoveInterval));
                }
                else
                {
                    // Set next move time to far future to disable movement
                    updateInfo.NextMoveTime = DateTime.Now.AddMilliseconds(1000);
                }
            }
        }

        /// <summary>
        /// Enable/disable attack for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable attack, false to disable</param>
        public static void SetAttackEnabled(int npcSessionID, bool enabled)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    // Reset next attack time to enable attack
                    updateInfo.NextAttackTime = DateTime.Now.AddMilliseconds(1000);
                }
                else
                {
                    // Set next attack time to far future to disable attack
                    updateInfo.NextAttackTime = DateTime.MaxValue;
                }
            }
        }

        /// <summary>
        /// Check if movement is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if movement is enabled</returns>
        public static bool IsMovementEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextMoveTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Check if attack is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if attack is enabled</returns>
        public static bool IsAttackEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextAttackTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Enable/disable respawn for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="enabled">True to enable respawn, false to disable</param>
        /// <param name="respawnInterval">Respawn interval in milliseconds (default: 30000)</param>
        public static void SetRespawnEnabled(int npcSessionID, bool enabled, int respawnInterval = 10000)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                if (enabled)
                {
                    // Check if respawn is already enabled to prevent multiple scheduling
                    bool wasAlreadyEnabled = updateInfo.NextRespawnTime != DateTime.MaxValue;

                    updateInfo.RespawnInterval = respawnInterval;

                    // Only set new respawn time if not already enabled
                    if (!wasAlreadyEnabled)
                    {
                        updateInfo.NextRespawnTime = DateTime.Now.AddMilliseconds(respawnInterval);
                       // LogHelper.WriteLine(LogLevel.Info, $"NPC {updateInfo.NPC.Name} (ID: {updateInfo.NPC.FLD_PID}) respawn enabled, will respawn in {respawnInterval}ms");
                    }
                    else
                    {
                        //LogHelper.WriteLine(LogLevel.Info, $"NPC {updateInfo.NPC.Name} (ID: {updateInfo.NPC.FLD_PID}) respawn already enabled, keeping existing schedule");
                    }
                }
                else
                {
                    updateInfo.NextRespawnTime = DateTime.MaxValue;
                 //   LogHelper.WriteLine(LogLevel.Info, $"NPC {updateInfo.NPC.Name} (ID: {updateInfo.NPC.FLD_PID}) respawn disabled");
                }
            }
            else
            {
                LogHelper.WriteLine(LogLevel.Error, $"Attempted to set respawn for unregistered NPC with SessionID: {npcSessionID}");
            }
        }

        /// <summary>
        /// Check if respawn is enabled for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <returns>True if respawn is enabled</returns>
        public static bool IsRespawnEnabled(int npcSessionID)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                return updateInfo.NextRespawnTime != DateTime.MaxValue;
            }
            return false;
        }

        /// <summary>
        /// Update respawn interval for a specific NPC
        /// </summary>
        /// <param name="npcSessionID">NPC session ID</param>
        /// <param name="newInterval">New respawn interval in milliseconds</param>
        public static void UpdateRespawnInterval(int npcSessionID, int newInterval)
        {
            if (_npcUpdates.TryGetValue(npcSessionID, out var updateInfo))
            {
                updateInfo.RespawnInterval = newInterval;
                // Update next respawn time if respawn is enabled
                if (updateInfo.NextRespawnTime != DateTime.MaxValue)
                {
                    updateInfo.NextRespawnTime = DateTime.Now.AddMilliseconds(newInterval);
                }
            }
        }
        
        /// <summary>
        /// Main timer event that processes all NPC behavior updates
        /// Each NPC is processed based on its individual behavior schedule for intelligent actions
        /// </summary>
        private static void ProcessAllNPCUpdates(object sender, ElapsedEventArgs e)
        {
            try
            {
                var now = DateTime.Now;
                var processedCount = 0;
                var errorCount = 0;
                
                foreach (var kvp in _npcUpdates)
                {
                    var npcInfo = kvp.Value;
                    var npc = npcInfo.NPC;
                    
                    try
                    {
                        // Skip if NPC is disposed (but not if just dead - dead NPCs need respawn processing)
                        if (npc.NPC_Removed)
                        {
                            // Only log occasionally to avoid spam
                            if (now.Second % 60 == 0 && now.Millisecond < 200)
                            {
                                //LogHelper.WriteLine(LogLevel.Info, $"Skipping NPC {npc.Name} (ID: {npc.FLD_PID}) - Removed: {npc.NPC_Removed}");
                            }
                            continue;
                        }

                        // Process respawn first (for dead NPCs)
                        if (now >= npcInfo.NextRespawnTime)
                        {
                            ProcessNPCRespawn(npc, npcInfo, now);
                            // Process behavior next time
                            continue;
                        }

                        // Only process behavior for alive NPCs
                        if (!npc.NPCDeath)
                        {
                            // Process behavior (includes movement, attack, chase, etc.)
                            if (now >= npcInfo.NextMoveTime)
                            {
                                ProcessNPC(npc, npcInfo, now);
                                processedCount++;
                            }
                            else if ((npcInfo.NextMoveTime - now).TotalSeconds > 31)
                            {
                                NPCBehaviorManager.ResetNPCState(npcInfo.NPC);
                            }
                        }
                        else
                        {
                            // Log dead NPCs occasionally
                            if (now.Second % 60 == 0 && now.Millisecond < 200)
                            {
                              //  LogHelper.WriteLine(LogLevel.Info, $"Skipping NPC {npc.Name} (ID: {npc.FLD_PID}) - Dead: {npc.NPCDeath}, Removed: {npc.NPC_Removed}");
                            }
                        }

                        npcInfo.LastUpdateTime = now;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        LogHelper.WriteLine(LogLevel.Info, $"Error processing NPC {npc.NPC_SessionID} ({npc.Name}): {ex.Message}");

                        // Track individual NPC error count instead of global error count
                        if (!npcInfo.ErrorCount.HasValue)
                            npcInfo.ErrorCount = 0;

                        npcInfo.ErrorCount++;

                        // Remove NPCs that consistently fail (more than 50 consecutive errors)
                        if (npcInfo.ErrorCount > 50)
                        {
                            LogHelper.WriteLine(LogLevel.Error, $"Removing problematic NPC {npc.NPC_SessionID} ({npc.Name}) after {npcInfo.ErrorCount} consecutive errors");
                            _npcUpdates.TryRemove(kvp.Key, out _);
                        }
                    }
                }
                
                if (now.Second % 30 == 0 && now.Millisecond < 200)
                {
                    var aliveCount = 0;
                    var deadCount = 0;
                    var respawnEnabledCount = 0;

                    foreach (var kvp in _npcUpdates)
                    {
                        var npcInfo = kvp.Value;
                        if (npcInfo.NPC.NPCDeath)
                            deadCount++;
                        else
                            aliveCount++;

                        if (npcInfo.NextRespawnTime != DateTime.MaxValue)
                            respawnEnabledCount++;
                    }

                    LogHelper.WriteLine(LogLevel.Info, $"NPCTimerManager: Total {_npcUpdates.Count} NPCs (Alive: {aliveCount}, Dead: {deadCount}, Respawn enabled: {respawnEnabledCount}), {processedCount} moves this cycle");
                }

                // Clean up old throttling entries every 5 minutes
                if (now.Minute % 5 == 0 && now.Second % 30 == 0 && now.Millisecond < 200)
                {
                    NPCBehaviorManager.CleanupOldEntries();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Critical error in NPCTimerManager: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Process NPC behavior (movement, attack, chase, etc.) - this is the core behavior processing
        /// </summary>
        private static void ProcessNPC(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                // Skip if NPC is not active or is special non-moving NPC
                if (npc.IsNpc != 0 || npc.NPCDeath)
                {
                    // Schedule next check with longer interval for inactive NPCs
                    npcInfo.NextMoveTime = now.AddMilliseconds(5000);
                    return;
                }

                // Skip special NPCs that don't move
                if (npc.FLD_PID == 16431 || npc.FLD_PID == 16430 || npc.FLD_PID == 16435)
                {
                    npcInfo.NextMoveTime = now.AddMilliseconds(5000); // Check much less frequently
                    return;
                }

                // Only process behavior for NPCs with attack capability
                if (npc.IsNpc != 1 && npc.FLD_AT > 0.0)
                {
                    ProcessNPCBehavior(npc, npcInfo, now);
                }
                else
                {
                    // For non-combat NPCs, use longer intervals
                    npcInfo.NextMoveTime = now.AddMilliseconds(5000);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error in ProcessNPCMove for NPC {npc.NPC_SessionID}: {ex.Message}");
                // On error, schedule next check with longer interval
                npcInfo.NextMoveTime = now.AddMilliseconds(5000);
            }
        }

        /// <summary>
        /// Process intelligent NPC behavior based on current situation
        /// </summary>
        private static void ProcessNPCBehavior(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                Players targetPlayer = null;
                NPCBehaviorType behavior;

                // Refresh nearby players periodically to fix AOI sync issues
                // This ensures NPCs can detect players who moved from far to near
                if ((now - npcInfo.LastUpdateTime).TotalSeconds > 5)
                {
                    RefreshNPCPlayerList(npc);
                }

                // Find closest target player based on NPC type
                if (npc.PlayList != null && npc.PlayList.Count > 0)
                {
                    Players closestPlayer = null;
                    double closestDistance = double.MaxValue;

                    // Create a safe copy of the PlayList to avoid enumeration issues
                    var playersCopy = new Dictionary<int, Players>();
                    try
                    {
                        // Use ToArray() to get a snapshot of the ThreadSafeDictionary
                        var playersArray = npc.PlayList.ToArray();
                        foreach (var kvp in playersArray)
                        {
                            playersCopy[kvp.Key] = kvp.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"Error copying PlayList for NPC {npc.NPC_SessionID}: {ex.Message}");
                        playersCopy.Clear();
                    }

                    // Clean up invalid players from PlayList first
                    var playersToRemove = new List<int>();
                    foreach (var kvp in playersCopy)
                    {
                        var player = kvp.Value;
                        if (player == null ||  player.MapID != npc.Rxjh_Map || player.Client?.Running != true)
                        {
                            playersToRemove.Add(kvp.Key);
                            continue;
                        }

                        var distance = NPCBehaviorManager.CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, player.PosX, player.PosY);

                        // For aggressive NPCs (FLD_AUTO = 1), check chase range
                        if (npc.FLD_AUTO == 1 && distance <= NPCBehaviorManager.CHASE_RANGE)
                        {
                            if (distance < closestDistance)
                            {
                                closestDistance = distance;
                                closestPlayer = player;
                            }
                        }
                        // For passive NPCs (FLD_AUTO = 0), once they have players in PlayList (been attacked),
                        // they behave like aggressive NPCs and will chase within chase range
                        else if (npc.FLD_AUTO == 0 && distance <= NPCBehaviorManager.CHASE_RANGE)
                        {
                            if (distance < closestDistance)
                            {
                                closestDistance = distance;
                                closestPlayer = player;
                            }
                        }
                    }

                    // Remove invalid players safely
                    foreach (var sessionId in playersToRemove)
                    {
                        try
                        {
                            npc.PlayList_Remove(npc.PlayList.TryGetValue(sessionId, out var playerToRemove) ? playerToRemove : null);
                        }
                        catch (Exception ex)
                        {
                            LogHelper.WriteLine(LogLevel.Info, $"Error removing player {sessionId} from NPC {npc.NPC_SessionID} PlayList: {ex.Message}");
                        }
                    }

                    // Set the closest player as target
                    if (closestPlayer != null)
                    {
                        targetPlayer = closestPlayer;
                        npc.Play_Add(closestPlayer, 0);
                    }
                }

                // If no target found in PlayList, scan for nearby players (especially for aggressive NPCs)
                if (targetPlayer == null && npc.FLD_AUTO == 1)
                {
                    targetPlayer = ScanForNearbyPlayers(npc);
                }

                // Determine behavior based on current situation
                behavior = NPCBehaviorManager.DetermineBehavior(npc, targetPlayer);

                npcInfo.LastBehavior = behavior;

                // Execute the determined behavior
                NPCBehaviorManager.ExecuteBehavior(npc, behavior, targetPlayer);

                // Schedule next behavior check based on current behavior
                ScheduleNextBehaviorCheck(npcInfo, now, behavior, targetPlayer != null);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error in ProcessNPCBehavior for NPC {npc.NPC_SessionID}: {ex.Message}");
                // On error, schedule next check with default interval
                npcInfo.NextMoveTime = now.AddMilliseconds(npcInfo.MoveInterval);
            }
        }

        /// <summary>
        /// Schedule next behavior check based on current behavior and situation
        /// </summary>
        private static void ScheduleNextBehaviorCheck(NPCUpdateInfo npcInfo, DateTime now, NPCBehaviorType behavior, bool hasTarget)
        {
            var nextInterval = behavior switch
                    {
                        NPCBehaviorType.Attack => hasTarget ? 1000 : 2000,// When attacking, check frequently to continue attacking
                        NPCBehaviorType.ReturnToSpawn => 2000,// When returning to spawn, check less frequently
                        _ => hasTarget ? 1500 : RNG.Next(3000, 6000),// When idle, check less frequently but still scan for players
                    };

            // Add small random variation to prevent synchronized behavior
            var randomOffset = RNG.Next(-200, 200);
            nextInterval = Math.Max(500, nextInterval + randomOffset);

            npcInfo.NextMoveTime = now.AddMilliseconds(nextInterval);
        }


        /// <summary>
        /// Process respawn for a specific NPC
        /// </summary>
        private static void ProcessNPCRespawn(NpcClass npc, NPCUpdateInfo npcInfo, DateTime now)
        {
            try
            {
                // Call the NPC's respawn logic
                npc.ProcessAutomaticRespawn();
                npcInfo.NextRespawnTime = DateTime.MaxValue;

                // After respawn, immediately refresh player list to ensure NPC can detect nearby players
                RefreshNPCPlayerList(npc);

                // Reset next move time to ensure NPC starts behaving immediately
                npcInfo.NextMoveTime = now.AddMilliseconds(RNG.Next(1000, 2000));

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error in ProcessNPCRespawn for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Refresh NPC's player list to fix AOI synchronization issues
        /// </summary>
        private static void RefreshNPCPlayerList(NpcClass npc)
        {
            try
            {
                if (npc.PlayList == null) return;

                // Scan for nearby players using AOI system
                var aoiGrids = AOI.AOIManager.Instance.GetAOIGrids(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map);

                foreach (var grid in aoiGrids)
                {
                    grid.ForEachPlayer(player =>
                    {
                        if (player != null && player.Client?.Running == true &&
                            player.NhanVat_HP > 0 && !player.PlayerTuVong &&
                            player.MapID == npc.Rxjh_Map)
                        {
                            var distance = NPCBehaviorManager.CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, player.PosX, player.PosY);

                            // Add player to NPC's awareness if within detection range
                            if (distance <= NPCBehaviorManager.CHASE_RANGE * 1.2) // Slightly larger range for detection
                            {
                                try
                                {
                                    if (!npc.PlayList.ContainsKey(player.SessionID))
                                    {
                                        npc.PlayList_Add(player);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LogHelper.WriteLine(LogLevel.Info, $"Error adding player {player.SessionID} to NPC {npc.NPC_SessionID} PlayList: {ex.Message}");
                                }
                            }
                            // Remove player if too far away
                            else if (distance > NPCBehaviorManager.CHASE_RANGE * 1.5)
                            {
                                try
                                {
                                    if (npc.PlayList.ContainsKey(player.SessionID))
                                    {
                                        npc.PlayList_Remove(player);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    LogHelper.WriteLine(LogLevel.Info, $"Error removing player {player.SessionID} from NPC {npc.NPC_SessionID} PlayList: {ex.Message}");
                                }
                            }
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error in RefreshNPCPlayerList for NPC {npc.NPC_SessionID}: {ex.Message}");
            }
        }

        /// <summary>
        /// Scan for nearby players for aggressive NPCs
        /// </summary>
        private static Players ScanForNearbyPlayers(NpcClass npc)
        {
            try
            {
                if (npc.PlayList == null) return null;

                Players closestPlayer = null;
                double closestDistance = double.MaxValue;

                var aoiGrids = AOI.AOIManager.Instance.GetAOIGrids(npc.Rxjh_X, npc.Rxjh_Y, npc.Rxjh_Map);

                foreach (var grid in aoiGrids)
                {
                    grid.ForEachPlayer(player =>
                    {
                        if (player != null && player.Client?.Running == true &&
                            player.NhanVat_HP > 0 && !player.PlayerTuVong &&
                            player.MapID == npc.Rxjh_Map)
                        {
                            var distance = NPCBehaviorManager.CalculateDistance(npc.Rxjh_X, npc.Rxjh_Y, player.PosX, player.PosY);

                            if (distance <= NPCBehaviorManager.CHASE_RANGE && distance < closestDistance)
                            {
                                closestDistance = distance;
                                closestPlayer = player;
                            }
                        }
                    });
                }

                // Add the found player to PlayList safely
                if (closestPlayer != null)
                {
                    try
                    {
                        npc.PlayList_Add(closestPlayer);
                    }
                    catch (Exception ex)
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"Error adding closest player {closestPlayer.SessionID} to NPC {npc.NPC_SessionID} PlayList: {ex.Message}");
                    }
                }

                return closestPlayer;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error in ScanForNearbyPlayers for NPC {npc.NPC_SessionID}: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Get statistics about the timer manager
        /// </summary>
        public static (int RegisteredNPCs, bool IsRunning) GetStatistics()
        {
            return (_npcUpdates.Count, _globalUpdateTimer.Enabled);
        }

        /// <summary>
        /// Shutdown the timer manager
        /// </summary>
        public static void Shutdown()
        {
            try
            {
                _globalUpdateTimer.Stop();
                _globalUpdateTimer.Dispose();
                _npcUpdates.Clear();
                _isInitialized = false;
                
                LogHelper.WriteLine(LogLevel.Info, "NPCTimerManager shutdown completed");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Info, $"Error during NPCTimerManager shutdown: {ex.Message}");
            }
        }
    }
    
    /// <summary>
    /// Information about NPC update timing
    /// </summary>
    public class NPCUpdateInfo
    {
        public NpcClass NPC { get; set; }
        public DateTime NextMoveTime { get; set; }
        public DateTime NextAttackTime { get; set; }
        public DateTime NextRespawnTime { get; set; }
        public DateTime LastUpdateTime { get; set; }
        public int MoveInterval { get; set; }
        public int RespawnInterval { get; set; }
        public int? ErrorCount { get; set; }
        public DateTime LastBehaviorTime { get; set; } = DateTime.Now;
        public NPCBehaviorType LastBehavior { get; set; } = NPCBehaviorType.Idle;
        public int StuckCounter { get; set; } = 0;
    }

}
