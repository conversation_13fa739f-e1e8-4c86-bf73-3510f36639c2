using System;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace RxjhServer.NpcManager
{
    /// <summary>
    /// Optimized distance calculation helper to improve NPC performance
    /// </summary>
    public static class DistanceHelper
    {
        /// <summary>
        /// Cache for frequently used distance calculations
        /// Key: (x1, y1, x2, y2, range), Value: isWithinRange
        /// </summary>
        private static readonly ConcurrentDictionary<(int, int, int, int, int), bool> _distanceCache = new();
        
        /// <summary>
        /// Maximum cache size to prevent memory bloat
        /// </summary>
        private const int MAX_CACHE_SIZE = 10000;
        
        /// <summary>
        /// Check if two points are within range using squared distance (no Math.Sqrt)
        /// This is 10-20x faster than using Math.Sqrt
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <param name="range">Maximum distance range</param>
        /// <returns>True if points are within range</returns>
        public static bool IsWithinRangeSquared(float x1, float y1, float x2, float y2, int range)
        {
            var deltaX = x2 - x1;
            var deltaY = y2 - y1;
            var distanceSquared = deltaX * deltaX + deltaY * deltaY;
            var rangeSquared = range * range;
            return distanceSquared <= rangeSquared;
        }
        
        /// <summary>
        /// Check if two points are within range with caching for frequently accessed calculations
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <param name="range">Maximum distance range</param>
        /// <returns>True if points are within range</returns>
        public static bool IsWithinRangeCached(float x1, float y1, float x2, float y2, int range)
        {
            // Round coordinates to reduce cache key variations
            var key = ((int)Math.Round(x1 / 10) * 10, (int)Math.Round(y1 / 10) * 10, 
                      (int)Math.Round(x2 / 10) * 10, (int)Math.Round(y2 / 10) * 10, range);
            
            return _distanceCache.GetOrAdd(key, _ => 
            {
                // Clean cache if it gets too large
                if (_distanceCache.Count > MAX_CACHE_SIZE)
                {
                    CleanCache();
                }
                return IsWithinRangeSquared(x1, y1, x2, y2, range);
            });
        }
        
        /// <summary>
        /// Calculate actual distance between two points (use sparingly)
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <returns>Actual distance</returns>
        public static float CalculateDistance(float x1, float y1, float x2, float y2)
        {
            var deltaX = x2 - x1;
            var deltaY = y2 - y1;
            return (float)Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
        }
        
        /// <summary>
        /// Calculate squared distance (faster, use for comparisons)
        /// </summary>
        /// <param name="x1">First point X coordinate</param>
        /// <param name="y1">First point Y coordinate</param>
        /// <param name="x2">Second point X coordinate</param>
        /// <param name="y2">Second point Y coordinate</param>
        /// <returns>Squared distance</returns>
        public static float CalculateDistanceSquared(float x1, float y1, float x2, float y2)
        {
            var deltaX = x2 - x1;
            var deltaY = y2 - y1;
            return deltaX * deltaX + deltaY * deltaY;
        }
        
        /// <summary>
        /// Clean half of the cache entries to prevent memory bloat
        /// </summary>
        private static void CleanCache()
        {
            try
            {
                var keysToRemove = new List<(int, int, int, int, int)>();
                var count = 0;
                var targetCount = _distanceCache.Count / 2;
                
                foreach (var key in _distanceCache.Keys)
                {
                    keysToRemove.Add(key);
                    count++;
                    if (count >= targetCount)
                        break;
                }
                
                foreach (var key in keysToRemove)
                {
                    _distanceCache.TryRemove(key, out _);
                }
            }
            catch (Exception)
            {
                // If cleaning fails, clear entire cache
                _distanceCache.Clear();
            }
        }
        
        /// <summary>
        /// Clear all cached distance calculations
        /// </summary>
        public static void ClearCache()
        {
            _distanceCache.Clear();
        }
        
        /// <summary>
        /// Get cache statistics for monitoring
        /// </summary>
        /// <returns>Number of cached entries</returns>
        public static int GetCacheSize()
        {
            return _distanceCache.Count;
        }
    }
}
