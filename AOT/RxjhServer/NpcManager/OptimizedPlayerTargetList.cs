using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;

namespace RxjhServer.NpcManager
{
	/// <summary>
	/// Optimized player target list that reduces sorting overhead and improves performance
	/// </summary>
	public class OptimizedPlayerTargetList
	{
		private readonly List<PlayGjClass> _targets = new();
		private readonly Dictionary<int, int> _playerToIndex = new();

		// Caching for performance
		private bool _needsSort = false;
		private PlayGjClass _cachedTopPlayer;
		private PlayGjTeamClass _cachedTopTeam;
		private DateTime _lastSortTime = DateTime.MinValue;
		private DateTime _lastTeamCalculation = DateTime.MinValue;

		// Configuration
		private static readonly TimeSpan SORT_COOLDOWN = TimeSpan.FromSeconds(1);
		private static readonly TimeSpan TEAM_CALC_COOLDOWN = TimeSpan.FromMilliseconds(500);

		/// <summary>
		/// Get the current number of targets
		/// </summary>
		public int Count => _targets.Count;

		/// <summary>
		/// Add damage for a player, creating new entry if needed
		/// </summary>
		/// <param name="playerId">Player session ID</param>
		/// <param name="damage">Damage amount to add</param>
		/// <param name="teamId">Player's team ID</param>
		public void AddDamage(int playerId, int damage, int teamId)
		{
			try
			{
				if (_playerToIndex.TryGetValue(playerId, out var index))
				{
					// Update existing player
					var target = _targets[index];
					target.Gjxl += damage;
					target.Gjsl++;
					target.TeamID = teamId;
				}
				else
				{
					// Add new player
					var newTarget = new PlayGjClass
					{
						PlayID = playerId,
						Gjxl = damage,
						Gjsl = 1,
						TeamID = teamId
					};

					_targets.Add(newTarget);
					_playerToIndex[playerId] = _targets.Count - 1;
				}

				// Mark as needing sort and invalidate cache
				_needsSort = true;
				_cachedTopPlayer = null;
				_cachedTopTeam = null;
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error in AddDamage: {ex.Message}");
			}
		}

		/// <summary>
		/// Get the top damage dealer (cached and optimized)
		/// </summary>
		/// <returns>Top damage dealer or default if none</returns>
		public PlayGjClass GetTopPlayer()
		{
			try
			{
				if (_targets.Count == 0)
				{
					return new PlayGjClass { PlayID = 0, Gjxl = 0 };
				}

				// Return cached result if available and recent
				if (_cachedTopPlayer != null && !_needsSort)
				{
					return _cachedTopPlayer;
				}

				// Only sort if needed and cooldown has passed
				if (_needsSort && DateTime.Now - _lastSortTime > SORT_COOLDOWN)
				{
					SortTargets();
				}

				// Get top player (even if not recently sorted)
				if (_targets.Count > 0)
				{
					_cachedTopPlayer = _targets[0];
					return _cachedTopPlayer;
				}

				return new PlayGjClass { PlayID = 0, Gjxl = 0 };
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error in GetTopPlayer: {ex.Message}");
				return new PlayGjClass { PlayID = 0, Gjxl = 0 };
			}
		}

		/// <summary>
		/// Get the top damage dealing team (cached and optimized)
		/// </summary>
		/// <returns>Top team or default if none</returns>
		public PlayGjTeamClass GetTopTeam()
		{
			try
			{
				if (_targets.Count == 0)
				{
					return new PlayGjTeamClass { TeamID = 0, Gjxl_team = 0 };
				}

				// Return cached result if available and recent
				if (_cachedTopTeam != null && DateTime.Now - _lastTeamCalculation < TEAM_CALC_COOLDOWN)
				{
					return _cachedTopTeam;
				}

				// Calculate team damages
				var teamDamages = new Dictionary<int, double>();

				foreach (var target in _targets.Where(t => t.TeamID > 0))
				{
					if (teamDamages.ContainsKey(target.TeamID))
					{
						teamDamages[target.TeamID] += target.Gjxl;
					}
					else
					{
						teamDamages[target.TeamID] = target.Gjxl;
					}
				}

				if (teamDamages.Count > 0)
				{
					var topTeam = teamDamages.OrderByDescending(kvp => kvp.Value).First();
					_cachedTopTeam = new PlayGjTeamClass
					{
						TeamID = topTeam.Key,
						Gjxl_team = topTeam.Value
					};
				}
				else
				{
					_cachedTopTeam = new PlayGjTeamClass { TeamID = 0, Gjxl_team = 0 };
				}

				_lastTeamCalculation = DateTime.Now;
				return _cachedTopTeam;
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error in GetTopTeam: {ex.Message}");
				return new PlayGjTeamClass { TeamID = 0, Gjxl_team = 0 };
			}
		}

		/// <summary>
		/// Get all targets as a list (for compatibility)
		/// </summary>
		/// <returns>List of all targets</returns>
		public List<PlayGjClass> GetAllTargets()
		{
			return new List<PlayGjClass>(_targets);
		}

		/// <summary>
		/// Clear all targets
		/// </summary>
		public void Clear()
		{
			_targets.Clear();
			_playerToIndex.Clear();
			_cachedTopPlayer = null;
			_cachedTopTeam = null;
			_needsSort = false;
		}

		/// <summary>
		/// Remove a specific player from targets
		/// </summary>
		/// <param name="playerId">Player ID to remove</param>
		/// <returns>True if player was removed</returns>
		public bool RemovePlayer(int playerId)
		{
			try
			{
				if (_playerToIndex.TryGetValue(playerId, out var index))
				{
					_targets.RemoveAt(index);
					_playerToIndex.Remove(playerId);

					// Update indices for remaining players
					for (int i = index; i < _targets.Count; i++)
					{
						_playerToIndex[_targets[i].PlayID] = i;
					}

					// Invalidate cache
					_cachedTopPlayer = null;
					_cachedTopTeam = null;
					_needsSort = true;

					return true;
				}
				return false;
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error in RemovePlayer: {ex.Message}");
				return false;
			}
		}

		/// <summary>
		/// Sort targets by damage (descending)
		/// </summary>
		private void SortTargets()
		{
			try
			{
				_targets.Sort((a, b) => b.Gjxl.CompareTo(a.Gjxl));

				// Rebuild index mapping after sort
				_playerToIndex.Clear();
				for (int i = 0; i < _targets.Count; i++)
				{
					_playerToIndex[_targets[i].PlayID] = i;
				}

				_lastSortTime = DateTime.Now;
				_needsSort = false;
				_cachedTopPlayer = null; // Will be recalculated on next access
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(LogLevel.Error, $"Error in SortTargets: {ex.Message}");
			}
		}

		/// <summary>
		/// Get statistics about the target list
		/// </summary>
		/// <returns>Statistics string</returns>
		public string GetStatistics()
		{
			return $"Targets: {_targets.Count}, NeedsSort: {_needsSort}, LastSort: {_lastSortTime:HH:mm:ss}";
		}
	}
}
