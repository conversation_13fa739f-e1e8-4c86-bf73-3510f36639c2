using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Helpers;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Specialized thread pool for AOI operations to improve performance
    /// </summary>
    public class AOIThreadPool
    {
        #region Singleton Pattern
        
        private static readonly Lazy<AOIThreadPool> _instance = new Lazy<AOIThreadPool>(() => new AOIThreadPool());
        public static AOIThreadPool Instance => _instance.Value;
        
        #endregion
        
        #region Thread Pool Configuration
        
        /// <summary>
        /// Number of worker threads for AOI operations
        /// </summary>
        private readonly int _workerThreadCount;
        
        /// <summary>
        /// Maximum queue size before blocking
        /// </summary>
        private const int MAX_QUEUE_SIZE = 10000;
        
        /// <summary>
        /// Task queue for AOI operations
        /// </summary>
        private readonly ConcurrentQueue<AOITask> _taskQueue;
        
        /// <summary>
        /// Semaphore to control queue size
        /// </summary>
        private readonly SemaphoreSlim _queueSemaphore;
        
        /// <summary>
        /// Cancellation token for shutdown
        /// </summary>
        private readonly CancellationTokenSource _cancellationTokenSource;
        
        /// <summary>
        /// Worker threads
        /// </summary>
        private readonly Thread[] _workerThreads;
        
        /// <summary>
        /// Thread pool statistics
        /// </summary>
        private volatile int _tasksProcessed;
        private volatile int _tasksQueued;
        private volatile int _tasksDropped;
        
        #endregion
        
        #region Task Types
        
        /// <summary>
        /// Types of AOI tasks
        /// </summary>
        public enum AOITaskType
        {
            PlayerUpdate,
            NPCUpdate,
            GridUpdate,
            BatchUpdate,
            CacheCleanup
        }
        
        /// <summary>
        /// AOI task wrapper
        /// </summary>
        public class AOITask
        {
            public AOITaskType Type { get; set; }
            public Action Action { get; set; }
            public int Priority { get; set; } // Higher number = higher priority
            public DateTime CreatedTime { get; set; }
            public string Description { get; set; }
            
            public AOITask(AOITaskType type, Action action, int priority = 0, string description = "")
            {
                Type = type;
                Action = action;
                Priority = priority;
                CreatedTime = DateTime.Now;
                Description = description;
            }
        }
        
        #endregion
        
        #region Constructor
        
        private AOIThreadPool()
        {
            // Calculate optimal thread count based on CPU cores
            _workerThreadCount = Math.Max(2, Environment.ProcessorCount / 2);
            
            _taskQueue = new ConcurrentQueue<AOITask>();
            _queueSemaphore = new SemaphoreSlim(MAX_QUEUE_SIZE, MAX_QUEUE_SIZE);
            _cancellationTokenSource = new CancellationTokenSource();
            
            // Initialize worker threads
            _workerThreads = new Thread[_workerThreadCount];
            for (int i = 0; i < _workerThreadCount; i++)
            {
                _workerThreads[i] = new Thread(WorkerThreadLoop)
                {
                    Name = $"AOI-Worker-{i}",
                    IsBackground = true
                };
                _workerThreads[i].Start();
            }
            
            LogHelper.WriteLine(LogLevel.Info, $"AOI Thread Pool initialized with {_workerThreadCount} worker threads");
        }
        
        #endregion
        
        #region Task Scheduling
        
        /// <summary>
        /// Queue a high-priority player update task
        /// </summary>
        public bool QueuePlayerUpdate(Players player, Action updateAction)
        {
            return QueueTask(new AOITask(
                AOITaskType.PlayerUpdate, 
                updateAction, 
                priority: 10, 
                description: $"Player update for {player.CharacterName}"
            ));
        }
        
        /// <summary>
        /// Queue an NPC update task
        /// </summary>
        public bool QueueNPCUpdate(NpcClass npc, Action updateAction)
        {
            return QueueTask(new AOITask(
                AOITaskType.NPCUpdate, 
                updateAction, 
                priority: 5, 
                description: $"NPC update for {npc.Name}"
            ));
        }
        
        /// <summary>
        /// Queue a grid update task
        /// </summary>
        public bool QueueGridUpdate(AOIGrid grid, Action updateAction)
        {
            return QueueTask(new AOITask(
                AOITaskType.GridUpdate, 
                updateAction, 
                priority: 3, 
                description: $"Grid update for ({grid.GridX}, {grid.GridY})"
            ));
        }
        
        /// <summary>
        /// Queue a batch update task
        /// </summary>
        public bool QueueBatchUpdate(List<Players> players, Action batchAction)
        {
            return QueueTask(new AOITask(
                AOITaskType.BatchUpdate, 
                batchAction, 
                priority: 8, 
                description: $"Batch update for {players.Count} players"
            ));
        }
        
        /// <summary>
        /// Queue a cache cleanup task
        /// </summary>
        public bool QueueCacheCleanup(Action cleanupAction)
        {
            return QueueTask(new AOITask(
                AOITaskType.CacheCleanup, 
                cleanupAction, 
                priority: 1, 
                description: "Cache cleanup"
            ));
        }
        
        /// <summary>
        /// Queue a generic AOI task
        /// </summary>
        private bool QueueTask(AOITask task)
        {
            try
            {
                // Check if we can add to queue without blocking
                if (!_queueSemaphore.Wait(0))
                {
                    // Queue is full, drop low priority tasks
                    if (task.Priority < 5)
                    {
                        Interlocked.Increment(ref _tasksDropped);
                        LogHelper.WriteLine(LogLevel.Info, $"AOI task dropped due to full queue: {task.Description}");
                        return false;
                    }
                    
                    // For high priority tasks, wait a short time
                    if (!_queueSemaphore.Wait(100))
                    {
                        Interlocked.Increment(ref _tasksDropped);
                        LogHelper.WriteLine(LogLevel.Info, $"High priority AOI task dropped: {task.Description}");
                        return false;
                    }
                }
                
                _taskQueue.Enqueue(task);
                Interlocked.Increment(ref _tasksQueued);
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error queueing AOI task: {ex.Message}");
                return false;
            }
        }
        
        #endregion
        
        #region Worker Thread Logic
        
        /// <summary>
        /// Main worker thread loop
        /// </summary>
        private void WorkerThreadLoop()
        {
            var priorityQueue = new List<AOITask>();
            
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // Collect tasks from queue
                    priorityQueue.Clear();
                    var batchSize = Math.Min(10, _taskQueue.Count);
                    
                    for (int i = 0; i < batchSize && _taskQueue.TryDequeue(out var task); i++)
                    {
                        priorityQueue.Add(task);
                        _queueSemaphore.Release();
                    }
                    
                    if (priorityQueue.Count == 0)
                    {
                        // No tasks available, wait a bit
                        Thread.Sleep(10);
                        continue;
                    }
                    
                    // Sort by priority (higher priority first)
                    priorityQueue.Sort((a, b) => b.Priority.CompareTo(a.Priority));
                    
                    // Process tasks
                    foreach (var task in priorityQueue)
                    {
                        ProcessTask(task);
                    }
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error in AOI worker thread: {ex.Message}");
                    Thread.Sleep(100); // Prevent tight error loops
                }
            }
        }
        
        /// <summary>
        /// Process a single AOI task
        /// </summary>
        private void ProcessTask(AOITask task)
        {
            try
            {
                var startTime = DateTime.Now;
                
                // Execute the task
                task.Action?.Invoke();
                
                var duration = DateTime.Now - startTime;
                Interlocked.Increment(ref _tasksProcessed);
                
                // Log slow tasks
                if (duration.TotalMilliseconds > 50)
                {
                    LogHelper.WriteLine(LogLevel.Info, 
                        $"Slow AOI task: {task.Description} took {duration.TotalMilliseconds:F2}ms");
                }
                
                // Update performance monitor
                AOIPerformanceMonitor.Instance.RecordOperation(task.Type.ToString(), duration);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing AOI task {task.Description}: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Statistics and Monitoring
        
        /// <summary>
        /// Get thread pool statistics
        /// </summary>
        public string GetStatistics()
        {
            var queueSize = _taskQueue.Count;
            var availableSlots = _queueSemaphore.CurrentCount;
            
            return $"AOI ThreadPool Stats - Workers: {_workerThreadCount}, " +
                   $"Queue: {queueSize}/{MAX_QUEUE_SIZE}, " +
                   $"Available: {availableSlots}, " +
                   $"Processed: {_tasksProcessed}, " +
                   $"Queued: {_tasksQueued}, " +
                   $"Dropped: {_tasksDropped}";
        }
        
        /// <summary>
        /// Get current queue size
        /// </summary>
        public int GetQueueSize()
        {
            return _taskQueue.Count;
        }
        
        /// <summary>
        /// Check if thread pool is overloaded
        /// </summary>
        public bool IsOverloaded()
        {
            return _taskQueue.Count > MAX_QUEUE_SIZE * 0.8;
        }
        
        /// <summary>
        /// Reset statistics
        /// </summary>
        public void ResetStatistics()
        {
            _tasksProcessed = 0;
            _tasksQueued = 0;
            _tasksDropped = 0;
        }
        
        #endregion
        
        #region Shutdown
        
        /// <summary>
        /// Shutdown the thread pool gracefully
        /// </summary>
        public void Shutdown()
        {
            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Shutting down AOI Thread Pool...");
                
                _cancellationTokenSource.Cancel();
                
                // Wait for worker threads to finish
                foreach (var thread in _workerThreads)
                {
                    if (thread.IsAlive)
                    {
                        thread.Join(5000); // Wait up to 5 seconds
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Info, "AOI Thread Pool shutdown complete");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error during AOI Thread Pool shutdown: {ex.Message}");
            }
        }
        
        #endregion
    }
}
