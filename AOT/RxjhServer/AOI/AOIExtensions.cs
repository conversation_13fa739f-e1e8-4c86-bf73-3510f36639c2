using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Extension methods to integrate AOI system with existing game entities
    /// Provides seamless integration without breaking existing functionality
    /// </summary>
    public static class AOIExtensions
    {
        #region Player Extensions
        
        /// <summary>
        /// Update player's AOI using the new AOI system
        /// This replaces the old GetTheReviewRangePlayers method
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        /// <param name="useAOISystem">Whether to use the new AOI system (default: true)</param>
        public static void UpdateAOI(this Players player, bool useAOISystem = true)
        {
            try
            {
                if (player == null)
                {
                    return;
                }
                
                if (useAOISystem && AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use optimized AOI system with adaptive updates
                    AOIUpdateService.Instance.AdaptiveUpdatePlayer(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating AOI for player {player.CharacterName}: {ex.Message}");
                
                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Update player's AOI immediately without adaptive delays
        /// Use this for critical updates that need immediate processing
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        public static void UpdateAOIImmediate(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use direct AOI update bypassing adaptive delays
                    AOIUpdateService.Instance.UpdatePlayerAOI(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in immediate AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Force update player's AOI regardless of throttling or optimization
        /// Use this when player changes map or other critical events
        /// </summary>
        /// <param name="player">Player to force update AOI for</param>
        public static void ForceUpdateAOI(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                LogHelper.WriteLine(LogLevel.Debug, $"Force updating AOI for player {player.CharacterName}");

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Force immediate AOI update
                    AOIUpdateService.Instance.UpdatePlayerAOI(player);

                    // Also ensure player is properly added to AOI system
                    AOIManager.Instance.AddPlayer(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in force AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback force AOI update also failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Add player to AOI system when they enter the game
        /// </summary>
        /// <param name="player">Player to add</param>
        public static void AddToAOI(this Players player)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.AddPlayer(player);
                    //LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding player to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove player from AOI system when they leave the game
        /// </summary>
        /// <param name="player">Player to remove</param>
        public static void RemoveFromAOI(this Players player)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.RemovePlayer(player.SessionID);
                    LogHelper.WriteLine(LogLevel.Info, $"Player {player.CharacterName} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing player from AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update player position in AOI system when they move
        /// </summary>
        /// <param name="player">Player that moved</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        public static void UpdateAOIPosition(this Players player, float newX, float newY)
        {
            try
            {
                if (player != null)
                {
                    AOIManager.Instance.UpdatePlayerPosition(player, newX, newY);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player position in AOI: {ex.Message}");
            }
        }

        /// <summary>
        /// Update player's AOI specifically for movement events (optimized)
        /// </summary>
        /// <param name="player">Player that moved</param>
        public static void UpdateMovementAOI(this Players player)
        {
            try
            {
                if (player == null)
                {
                    return;
                }

                if (AOIConfiguration.Instance.ShouldUseAOI(player.MapID))
                {
                    // Use optimized movement AOI update
                    AOIUpdateService.Instance.UpdatePlayerMovementAOI(player);
                }
                else
                {
                    // Fallback to old system
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in movement AOI update for player {player.CharacterName}: {ex.Message}");

                // Fallback to old system on error
                try
                {
                    player.GetTheReviewRangePlayers();
                    player.GetReviewScopeNpc();
                    player.ScanGroundItems();
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Fallback movement AOI update also failed: {fallbackEx.Message}");
                }
            }
        }
        
        #endregion
        
        #region NPC Extensions
        
        /// <summary>
        /// Add NPC to AOI system when it spawns
        /// </summary>
        /// <param name="npc">NPC to add</param>
        public static void AddToAOI(this NpcClass npc)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.AddNPC(npc);
                   // LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding NPC to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove NPC from AOI system when it despawns
        /// </summary>
        /// <param name="npc">NPC to remove</param>
        public static void RemoveFromAOI(this NpcClass npc)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.RemoveNPC(npc.NPC_SessionID);
                    LogHelper.WriteLine(LogLevel.Info, $"NPC {npc.Name} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing NPC from AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update NPC position in AOI system when it moves
        /// </summary>
        /// <param name="npc">NPC that moved</param>
        /// <param name="newX">New X coordinate</param>
        /// <param name="newY">New Y coordinate</param>
        public static void UpdateAOIPosition(this NpcClass npc, float newX, float newY)
        {
            try
            {
                if (npc != null)
                {
                    AOIManager.Instance.UpdateNPCPosition(npc, newX, newY);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC position in AOI: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Ground Item Extensions
        
        /// <summary>
        /// Add ground item to AOI system when it spawns
        /// </summary>
        /// <param name="item">Ground item to add</param>
        public static void AddToAOI(this GroundItem item)
        {
            try
            {
                if (item != null)
                {
                    AOIManager.Instance.AddGroundItem(item);
                   // LogHelper.WriteLine(LogLevel.Info, $"Ground item {item.id} added to AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error adding ground item to AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Remove ground item from AOI system when it despawns
        /// </summary>
        /// <param name="item">Ground item to remove</param>
        public static void RemoveFromAOI(this GroundItem item)
        {
            try
            {
                if (item != null)
                {
                    AOIManager.Instance.RemoveGroundItem(item.id);
                    LogHelper.WriteLine(LogLevel.Info, $"Ground item {item.id} removed from AOI system");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error removing ground item from AOI: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Batch Operations
        
        /// <summary>
        /// Update AOI for multiple players efficiently
        /// </summary>
        /// <param name="players">Players to update AOI for</param>
        public static void BatchUpdateAOI(this IEnumerable<Players> players)
        {
            try
            {
                var playerList = players?.Where(p => p != null).ToList();
                if (playerList != null && playerList.Count > 0)
                {
                    // Use optimized batch update with thread pool
                    AOIUpdateService.Instance.BatchUpdatePlayersOptimized(playerList);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in optimized batch AOI update: {ex.Message}");

                // Fallback to individual updates
                try
                {
                    var playerList = players?.Where(p => p != null).ToList();
                    if (playerList != null)
                    {
                        foreach (var player in playerList)
                        {
                            player.UpdateAOI();
                        }
                    }
                }
                catch (Exception fallbackEx)
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Error in batch AOI fallback: {fallbackEx.Message}");
                }
            }
        }
        
        #endregion
        
        #region Map Integration
        
        /// <summary>
        /// Initialize AOI system for a map
        /// </summary>
        /// <param name="mapID">Map ID to initialize</param>
        public static void InitializeAOI(int mapID)
        {
            try
            {
                AOIManager.Instance.InitializeMapGrids(mapID);
                LogHelper.WriteLine(LogLevel.Info, $"AOI system initialized for map {mapID}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error initializing AOI for map {mapID}: {ex.Message}");
            }
        }
        
   
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Check if AOI system is enabled
        /// </summary>
        /// <returns>True if AOI system is enabled</returns>
        public static bool IsAOIEnabled()
        {
            // This can be controlled by a configuration setting
            return true; // For now, always enabled
        }
        
        /// <summary>
        /// Get grid coordinates for a position
        /// </summary>
        /// <param name="posX">X coordinate</param>
        /// <param name="posY">Y coordinate</param>
        /// <param name="mapID">Map ID</param>
        /// <returns>Grid coordinates</returns>
        public static (int gridX, int gridY) GetGridCoordinates(float posX, float posY, int mapID)
        {
            return AOIManager.Instance.GetGridCoordinates(posX, posY, mapID);
        }
        
        #endregion
    }
    
    /// <summary>
    /// AOI statistics for monitoring and debugging
    /// </summary>
    public class AOIStatistics
    {
        public int MapID { get; set; }
        public int TotalGrids { get; set; }
        public int ActiveGrids { get; set; }
        public int TotalPlayers { get; set; }
        public int TotalNPCs { get; set; }
        public int TotalGroundItems { get; set; }
        public int DirtyGrids { get; set; }
        
        public override string ToString()
        {
            return $"AOI Stats - Map:{MapID} Grids:{ActiveGrids}/{TotalGrids} " +
                   $"Players:{TotalPlayers} NPCs:{TotalNPCs} Items:{TotalGroundItems} Dirty:{DirtyGrids}";
        }
    }
}
