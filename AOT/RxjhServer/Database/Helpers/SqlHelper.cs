using System;
using System.Data;
using Microsoft.Data.SqlClient;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace RxjhServer.Database.Helpers
{
    /// <summary>
    /// SQL Helper class cho ADO.NET operations - AOT compatible
    /// </summary>
    public static class SqlHelper
    {
        /// <summary>
        /// Thực thi query và trả về DataTable
        /// </summary>
        public static DataTable ExecuteQuery(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            var dataTable = new DataTable();
            using var reader = command.ExecuteReader();
            dataTable.Load(reader);
            return dataTable;
        }

        /// <summary>
        /// Thực thi query async và trả về DataTable
        /// </summary>
        public static async Task<DataTable> ExecuteQueryAsync(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            var dataTable = new DataTable();
            using var reader = await command.ExecuteReaderAsync();
            dataTable.Load(reader);
            return dataTable;
        }

        /// <summary>
        /// Thực thi stored procedure và trả về DataTable
        /// </summary>
        public static DataTable ExecuteStoredProcedure(SqlConnection connection, string procedureName, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            var dataTable = new DataTable();
            using var reader = command.ExecuteReader();
            dataTable.Load(reader);
            return dataTable;
        }

        /// <summary>
        /// Thực thi stored procedure async và trả về DataTable
        /// </summary>
        public static async Task<DataTable> ExecuteStoredProcedureAsync(SqlConnection connection, string procedureName, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            var dataTable = new DataTable();
            using var reader = await command.ExecuteReaderAsync();
            dataTable.Load(reader);
            return dataTable;
        }

        /// <summary>
        /// Thực thi command và trả về scalar value
        /// </summary>
        public static object? ExecuteScalar(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteScalar();
        }

        /// <summary>
        /// Thực thi command async và trả về scalar value
        /// </summary>
        public static async Task<object?> ExecuteScalarAsync(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return await command.ExecuteScalarAsync();
        }

        /// <summary>
        /// Thực thi stored procedure và trả về scalar value
        /// </summary>
        public static object? ExecuteStoredProcedureScalar(SqlConnection connection, string procedureName, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteScalar();
        }

        /// <summary>
        /// Thực thi command và trả về số rows affected
        /// </summary>
        public static int ExecuteNonQuery(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteNonQuery();
        }

        /// <summary>
        /// Thực thi command async và trả về số rows affected
        /// </summary>
        public static async Task<int> ExecuteNonQueryAsync(SqlConnection connection, string sql, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(sql, connection);
            command.CommandType = CommandType.Text;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Thực thi stored procedure và trả về số rows affected
        /// </summary>
        public static int ExecuteStoredProcedureNonQuery(SqlConnection connection, string procedureName, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return command.ExecuteNonQuery();
        }

        /// <summary>
        /// Thực thi stored procedure async và trả về số rows affected
        /// </summary>
        public static async Task<int> ExecuteStoredProcedureNonQueryAsync(SqlConnection connection, string procedureName, SqlParameter[]? parameters = null)
        {
            using var command = new SqlCommand(procedureName, connection);
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }

            return await command.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Tạo SqlParameter một cách an toàn
        /// </summary>
        public static SqlParameter CreateParameter(string parameterName, SqlDbType sqlDbType, object? value)
        {
            return new SqlParameter(parameterName, sqlDbType) { Value = value ?? DBNull.Value };
        }

        /// <summary>
        /// Tạo SqlParameter với size
        /// </summary>
        public static SqlParameter CreateParameter(string parameterName, SqlDbType sqlDbType, int size, object? value)
        {
            return new SqlParameter(parameterName, sqlDbType, size) { Value = value ?? DBNull.Value };
        }

        /// <summary>
        /// Tạo array SqlParameter từ dictionary
        /// </summary>
        public static SqlParameter[] CreateParameters(Dictionary<string, object?> parameters)
        {
            var sqlParams = new List<SqlParameter>();
            foreach (var param in parameters)
            {
                sqlParams.Add(new SqlParameter(param.Key, param.Value ?? DBNull.Value));
            }
            return sqlParams.ToArray();
        }

        /// <summary>
        /// Safe convert DBNull to nullable type
        /// </summary>
        public static T? SafeConvert<T>(object? value) where T : struct
        {
            if (value == null || value == DBNull.Value)
                return null;
            return (T)Convert.ChangeType(value, typeof(T));
        }

        /// <summary>
        /// Safe convert DBNull to string
        /// </summary>
        public static string? SafeConvertString(object? value)
        {
            if (value == null || value == DBNull.Value)
                return null;
            return value.ToString();
        }

        /// <summary>
        /// Safe convert DBNull to int with default value
        /// </summary>
        public static int SafeConvertToInt(object? value, int defaultValue = 0)
        {
            if (value == null || value == DBNull.Value)
                return defaultValue;

            try
            {
                return Convert.ToInt32(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Safe convert DBNull to long with default value
        /// </summary>
        public static long SafeConvertToLong(object? value, long defaultValue = 0L)
        {
            if (value == null || value == DBNull.Value)
                return defaultValue;

            try
            {
                return Convert.ToInt64(value);
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Safe convert DBNull to string with default value
        /// </summary>
        public static string SafeConvertToString(object? value, string defaultValue = "")
        {
            if (value == null || value == DBNull.Value)
                return defaultValue;

            try
            {
                return value.ToString() ?? defaultValue;
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
