using System;

namespace RxjhServer;

public class X_Cong_Kich_Xac_Nhan_Loai
{
	private int int_0;

	private int int_1;

	private int int_2;

	private bool bool_0;

	private int int_3;

	private DateTime dateTime_0;

	private int int_4;

	public int NhanVat_ID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int BiCongKichNhanVat_ID
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int VoCong_ID
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public bool CongKichTrangThai
	{
		get
		{
			return bool_0;
		}
		set
		{
			bool_0 = value;
		}
	}

	public int CongKichLoaiHinh
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public DateTime ThoiGianTanCong
	{
		get
		{
			return dateTime_0;
		}
		set
		{
			dateTime_0 = value;
		}
	}

	public int CongKichKhoangCach
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public X_Cong_Kich_Xac_Nhan_Loai()
	{
		BiCongKichNhanVat_ID = 0;
		NhanVat_ID = 0;
		VoCong_ID = 0;
		CongKichTrangThai = false;
		CongKichLoaiHinh = 0;
		CongKichKhoangCach = 0;
		ThoiGianTanCong = DateTime.Now;
	}

	public void KhoiTao(int BiCongKichNhanVat_ID_, int NhanVat_ID_, int VoCong_ID_, int CongKichLoaiHinh_, int CongKichKhoangCach_)
	{
		BiCongKichNhanVat_ID = BiCongKichNhanVat_ID_;
		NhanVat_ID = NhanVat_ID_;
		VoCong_ID = VoCong_ID_;
		CongKichLoaiHinh = CongKichLoaiHinh_;
		CongKichKhoangCach = CongKichKhoangCach_;
		ThoiGianTanCong = DateTime.Now;
		CongKichTrangThai = true;
	}
}
