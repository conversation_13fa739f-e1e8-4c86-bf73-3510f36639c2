using System;
using System.Collections.Generic;

namespace RxjhServer;

public class X_Cong_Kich_Loai : IDisposable
{
	public List<X_Quan_Cong_Kich_Loai> DanhNhieuMucTieu;

	private int _NhanVat_ID;

	private int _VoCong_ID;

	private int _CongKichLuc;

	private int _CongKichLoaiHinh;

	public int NhanVat_ID
	{
		get
		{
			return _NhanVat_ID;
		}
		set
		{
			_NhanVat_ID = value;
		}
	}

	public int VoCong_ID
	{
		get
		{
			return _VoCong_ID;
		}
		set
		{
			_VoCong_ID = value;
		}
	}

	public int CongKichLuc
	{
		get
		{
			return _CongKichLuc;
		}
		set
		{
			_CongKichLuc = value;
		}
	}

	public int CongKichLoaiHinh
	{
		get
		{
			return _CongKichLoaiHinh;
		}
		set
		{
			_CongKichLoaiHinh = value;
		}
	}

	public X_Cong_Kich_Loai(int NhanVat_ID_, int VoCongID_, int CongKichLuc_, int CongKichLoaiHinh_, int DanhNhieuMucTieul, bool <PERSON>ao_PhongLang)
	{
		NhanVat_ID = NhanVat_ID_;
		VoCong_ID = VoCongID_;
		CongKichLuc = CongKichLuc_;
		CongKichLoaiHinh = CongKichLoaiHinh_;
		if (DanhNhieuMucTieul == 4 || TuHao_PhongLang)
		{
			DanhNhieuMucTieu = new();
		}
	}

	public void Dispose()
	{
		if (DanhNhieuMucTieu != null)
		{
			DanhNhieuMucTieu.Clear();
			DanhNhieuMucTieu = null;
		}
	}

	public X_Cong_Kich_Loai(int NhanVat_ID_, int VoCongID_, int CongKichLuc_, int CongKichLoaiHinh_)
	{
		NhanVat_ID = NhanVat_ID_;
		VoCong_ID = VoCongID_;
		CongKichLuc = CongKichLuc_;
		CongKichLoaiHinh = CongKichLoaiHinh_;
	}
}
