namespace RxjhServer;

public class X_Dang_Cap_Ban_Thuong_Loai
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private string string_0;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private int int_9;

	private string string_1;

	public int DangCap
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int SinhMenh
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int VoHuan
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int NguyenBao
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public string TienBac
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int CongKich
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int PhongNgu
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int NeTranh
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int TrungDich
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int NoiCong
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int Set
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public string GoiVatPham
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}
}
