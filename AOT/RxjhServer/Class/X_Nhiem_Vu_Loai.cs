using System;
using System.Collections.Generic;

namespace RxjhServer;

public class X_Nhiem_Vu_Loai
{
	private string string_0 = string.Empty;

	private X_Toa_Do_Loai ToaDoClass_0 = new();

	private List<X_Nhiem_Vu_Can_Vat_Pham_Loai> NhiemVuCanVatPham_ = new();

	private List<X_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai> NhiemVuThuDuocVatPham_ = new();

	private List<X_Nhiem_Vu_Giai_Doan_Loai> _NhiemVu_GiaiDoan = new();

	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private string string_1;

	private int int_6;

	private int int_7;

	private byte[] _GiaiDoan_ThucHien_SoLieu;

	private byte[] _NhiemVu_byte;

	private string string_2;

	public byte[] NhiemVu_byte
	{
		get
		{
			return _NhiemVu_byte;
		}
		set
		{
			_NhiemVu_byte = value;
		}
	}

	public int NhiemVuID
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(_NhiemVu_byte, 0, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, _NhiemVu_byte, 0, 2);
		}
	}

	public int NhiemVuGiaiDoanID
	{
		get
		{
			var array = new byte[2];
			System.Buffer.BlockCopy(_NhiemVu_byte, 2, array, 0, 2);
			return BitConverter.ToInt16(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, _NhiemVu_byte, 2, 2);
		}
	}

	public byte[] GiaiDoan_ThucHien_SoLieu
	{
		get
		{
			return _GiaiDoan_ThucHien_SoLieu;
		}
		set
		{
			_GiaiDoan_ThucHien_SoLieu = value;
		}
	}

	public int NhiemVuSwitch
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int NhiemVu_Type
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public string NhiemVu_TieuSuNoiDung
	{
		get
		{
			return string_1;
		}
		set
		{
			string_1 = value;
		}
	}

	public int RwID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public string NhiemVu_Ten
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public int NhiemVuDangCap
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int NhiemVu_ChinhTa
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int NgheNghiep
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public X_Toa_Do_Loai ToaDo_NPC
	{
		get
		{
			return ToaDoClass_0;
		}
		set
		{
			ToaDoClass_0 = value;
		}
	}

	public int NpcID
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public string NPCNAME
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public List<X_Nhiem_Vu_Can_Vat_Pham_Loai> NhiemVuCanVatPham
	{
		get
		{
			return NhiemVuCanVatPham_;
		}
		set
		{
			NhiemVuCanVatPham_ = value;
		}
	}

	public List<X_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai> NhiemVu_NhanVatPham
	{
		get
		{
			return NhiemVuThuDuocVatPham_;
		}
		set
		{
			NhiemVuThuDuocVatPham_ = value;
		}
	}

	public int NhiemVuGiaiDoan_SoLuong
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public List<X_Nhiem_Vu_Giai_Doan_Loai> NhiemVu_GiaiDoan
	{
		get
		{
			return _NhiemVu_GiaiDoan;
		}
		set
		{
			_NhiemVu_GiaiDoan = value;
		}
	}

	public X_Nhiem_Vu_Loai(byte[] byte_0)
	{
		NhiemVu_byte = byte_0;
	}

	public X_Nhiem_Vu_Loai()
	{
		NhiemVu_byte = new byte[30];
	}

	public X_Nhiem_Vu_Loai GetRW(int int_8)
	{
		foreach (var value in World.NhiemVulist.Values)
		{
			if (value.RwID == int_8)
			{
				return value;
			}
		}
		return null;
	}
}
