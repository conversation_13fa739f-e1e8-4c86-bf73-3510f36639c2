using System;
using System.Collections.Generic;
using System.Threading;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Boss_Event_FireDragon_Loai
{
	public static int PhoBan_TienTrinh;

	public static int 讨伐副本占领者;

	public static int 火龙释放的状态ID;

	public DateTime 讨伐副本进行中结束ThoiGian;

	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	public static Dictionary<int, NpcClass> 讨伐战副本怪物数据列表;

	public X_Boss_Event_FireDragon_Loai()
	{
		PhoBan_TienTrinh = 1;
		讨伐战副本怪物数据列表 = new();
		讨伐副本进行中结束ThoiGian = DateTime.Now.AddMinutes(20.0);
		timer_0 = new(60000.0);
		timer_0.Elapsed += ThoiGian0结束事件;
		timer_0.Enabled = true;
		timer_0.AutoReset = true;
		var npcClass = World.AddNpc_CongThanhChien(16555, -65f, 49f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 10);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16556, -3f, -106f, 43001, QuaiXuatHien_DuyNhatMotLan: true, 10);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16557, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16557, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16557, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16557, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16600, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16602, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16602, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16604, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16604, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16604, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16604, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16607, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16607, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16607, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
		npcClass = World.AddNpc_CongThanhChien(16607, 0f, 0f, 43001, QuaiXuatHien_DuyNhatMotLan: false, 25);
		讨伐战副本怪物数据列表.Add(npcClass.NPC_SessionID, npcClass);
	}

	public void ThoiGian0结束事件(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Thảo phạt phó bản _ThoiGian Kết thúc sự kiện 1");
		}
		try
		{
			var num = (int)讨伐副本进行中结束ThoiGian.Subtract(DateTime.Now).TotalSeconds;
			if (PhoBan_TienTrinh == 2)
			{
				num = 0;
			}
			if (num > 0)
			{
				地狱火龙释放状态();
				return;
			}
			if (PhoBan_TienTrinh == 2)
			{
				讨伐副本结束提示(讨伐结果: true);
			}
			else
			{
				讨伐副本结束提示(讨伐结果: false);
			}
			if (讨伐战副本怪物数据列表 != null)
			{
				foreach (var value in 讨伐战副本怪物数据列表.Values)
				{
					value.GuiDiTuVongSoLieuWrapper(0);
					MapClass.delnpc(43001, value.NPC_SessionID);
				}
			}
			PhoBan_TienTrinh = 2;
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
			timer_1 = new(3000.0);
			timer_1.Elapsed += ThoiGian1结束事件;
			timer_1.Enabled = true;
			timer_1.AutoReset = true;
		}
		catch
		{
		}
	}

	public void ThoiGian1结束事件(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "讨伐副本_ThoiGian结束事件1");
		}
		try
		{
			timer_1.Enabled = false;
			timer_1.Close();
			timer_1.Dispose();
			讨伐奖励buff();
			Dispose();
			World.ThaoPhatPhanThuong();
			World.delNpc(43001, 16555);
		}
		catch
		{
		}
	}

	public void Dispose()
	{
		try
		{
			PhoBan_TienTrinh = 0;
			讨伐副本占领者 = 0;
			if (timer_0 != null)
			{
				timer_0.Enabled = false;
				timer_0.Close();
				timer_0.Dispose();
				timer_0 = null;
			}
			if (timer_1 != null)
			{
				timer_1.Enabled = false;
				timer_1.Close();
				timer_1.Dispose();
				timer_1 = null;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.MapID == 43001)
				{
					value.副本复活剩余次数 = 0;
					value.发送副本复活剩余次数();
					value.Mobile(420f, 1500f, 15f, 101, 0);
				}
			}
			if (讨伐战副本怪物数据列表 != null)
			{
				foreach (var value2 in 讨伐战副本怪物数据列表.Values)
				{
					value2.Dispose();
				}
				讨伐战副本怪物数据列表.Clear();
				讨伐战副本怪物数据列表 = null;
			}
			World.讨伐战副本 = null;
		}
		catch
		{
		}
	}

	public void 讨伐奖励buff()
	{
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.MapID == 43001)
			{
				if (value.AppendStatusList.ContainsKey(1008002150))
				{
					value.AppendStatusList[1008002150].ThoiGianKetThucSuKien();
				}
				X_Them_Vao_Trang_Thai_Loai x_Them_Vao_Trang_Thai_Loai = new(value, 7200000, 1008002150, 1);
				value.AppendStatusList.Add(x_Them_Vao_Trang_Thai_Loai.FLD_PID, x_Them_Vao_Trang_Thai_Loai);
				value.StatusEffect(BitConverter.GetBytes(1008002150), 1, 7200000);
				value.DuocPham_ThemVao_DoiQuai_CongKich += 100;
				value.DuocPham_ThemVao_DoiQuai_PhongNgu += 100;
				value.UpdateMartialArtsAndStatus();
				value.UpdateBroadcastCharacterData();
			}
		}
	}

	public static void 地狱火龙释放状态()
	{
		Random random = new(DateTime.Now.Millisecond);
		火龙释放的状态ID = random.Next(0, 5);
		var array = Converter.HexStringToByte("AA55AA0000000105A40008003B000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(火龙释放的状态ID), 0, array, 18, 4);
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.Client != null)
			{
				value.Client.Send(array, array.Length);
			}
		}
		switch (火龙释放的状态ID)
		{
		case 0:
		{
			var dictionary5 = MapClass.GetnpcPID(43001, 16600);
			{
				foreach (var value2 in dictionary5.Values)
				{
					value2.PhoBan_Event_FireDragon_StatusEffect();
					Thread.Sleep(1);
				}
				break;
			}
		}
		case 1:
		{
			var dictionary4 = MapClass.GetnpcPID(43001, 16602);
			{
				foreach (var value3 in dictionary4.Values)
				{
					value3.PhoBan_Event_FireDragon_StatusEffect();
					Thread.Sleep(1);
				}
				break;
			}
		}
		case 2:
		{
			var dictionary3 = MapClass.GetnpcPID(43001, 16604);
			{
				foreach (var value4 in dictionary3.Values)
				{
					value4.PhoBan_Event_FireDragon_StatusEffect();
					Thread.Sleep(1);
				}
				break;
			}
		}
		case 3:
		{
			var dictionary2 = MapClass.GetnpcPID(43001, 16607);
			{
				foreach (var value5 in dictionary2.Values)
				{
					value5.PhoBan_Event_FireDragon_StatusEffect();
					Thread.Sleep(1);
				}
				break;
			}
		}
		case 4:
		{
			var dictionary = MapClass.GetnpcPID(43001, 16557);
			{
				foreach (var value6 in dictionary.Values)
				{
					value6.PhoBan_Event_FireDragon_StatusEffect();
					Thread.Sleep(1);
				}
				break;
			}
		}
		}
	}

	public static void 进入副本提示(Players player, int time)
	{
		var array = Converter.HexStringToByte("AA55AA0075050105A40008000A00000001000100000000000000100E000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		if (player.Client != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(time), 0, array, 26, 4);
			player.Client.Send(array, array.Length);
		}
	}

	public static void 讨伐副本结束提示(bool 讨伐结果)
	{
		var array = Converter.HexStringToByte("AA55AA0075050105A400080006000000010078000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000055AA");
		if (讨伐结果)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(1), 0, array, 16, 1);
		}
		else
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(2), 0, array, 16, 1);
		}
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.MapID == 43001 && value.Client != null)
			{
				System.Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
				value.Client.Send(array, array.Length);
			}
		}
	}
}
