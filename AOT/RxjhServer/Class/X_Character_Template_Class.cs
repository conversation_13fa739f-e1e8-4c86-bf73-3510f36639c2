using System;

namespace RxjhServer;

public class X_Character_Template_Class
{
	public byte[] CharacterTemplate_byte;

	public byte KhuonMat1
	{
		get
		{
			return CharacterTemplate_byte[0];
		}
		set
		{
			CharacterTemplate_byte[0] = value;
		}
	}

	public short MauToc
	{
		get
		{
			return BitConverter.ToInt16(CharacterTemplate_byte, 1);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, CharacterTemplate_byte, 1, 2);
		}
	}

	public byte KieuToc11
	{
		get
		{
			return CharacterTemplate_byte[3];
		}
		set
		{
			CharacterTemplate_byte[3] = value;
		}
	}

	public byte AmThanh
	{
		get
		{
			return CharacterTemplate_byte[4];
		}
		set
		{
			CharacterTemplate_byte[4] = value;
		}
	}

	public byte GioiTinh
	{
		get
		{
			return CharacterTemplate_byte[5];
		}
		set
		{
			CharacterTemplate_byte[5] = value;
		}
	}

	public short KieuToc
	{
		get
		{
			return BitConverter.ToInt16(CharacterTemplate_byte, 6);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, CharacterTemplate_byte, 6, 2);
		}
	}

	public short KhuonMat
	{
		get
		{
			return BitConverter.ToInt16(CharacterTemplate_byte, 8);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, CharacterTemplate_byte, 8, 2);
		}
	}

	public X_Character_Template_Class(byte[] byte_0)
	{
		CharacterTemplate_byte = byte_0;
	}
}
