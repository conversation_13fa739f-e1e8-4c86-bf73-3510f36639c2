namespace RxjhServer;

public class X_Quan_Cong_Kich_Loai
{
	public int SoLuongHPConDuLai;

	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	public int NhanVat_ID
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int VoCong_ID
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int CongKichLuc
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int CongKichLoaiHinh
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int TongSoLuongHP
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public X_Quan_Cong_Kich_Loai(int int_5, int int_6, int int_7, int int_8)
	{
		NhanVat_ID = int_5;
		VoCong_ID = int_6;
		CongKichLuc = int_7;
		CongKichLoaiHinh = int_8;
	}
}
