using System;
using System.Timers;
using HeroYulgang.Database.FreeSql;
using HeroYulgang.Helpers;
using RxjhServer.Database;


namespace RxjhServer;

public class X_Bang_Phai_Chien_HuyetChien
{
	private object AsyncLock = new();

	private System.Timers.Timer timer_0;

	private System.Timers.Timer timer_1;

	private DateTime dateTime_0;

	private DateTime dateTime_1;

	public X_Bang_Chien_Class BangChienChuPhuong;

	public X_Bang_Chien_Class BangChienKhachHang;

	public int ChuPhuong_DiemSo;

	public int KhachHang_DiemSo;

	public int KetThuc;

	public int KetThuc2;

	public X_Bang_Phai_Chien_HuyetChien(X_Bang_Chien_Class BangChienClass_0, X_Bang_Chien_Class BangChienClass_1)
	{
		try
		{
			BangChienChuPhuong = BangChienClass_0;
			BangChienKhachHang = BangChienClass_1;
			ChuPhuong_DiemSo = 0;
			KhachHang_DiemSo = 0;
			KetThuc = 0;
			using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value in BangChienChuPhuong.DanhSachUngVien.Values)
				{
					value.AnnouncementOfSuccessfulMatchmaking(0);
				}
			}
			using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value2 in BangChienKhachHang.DanhSachUngVien.Values)
				{
					value2.AnnouncementOfSuccessfulMatchmaking(0);
				}
			}
			dateTime_0 = DateTime.Now.AddMinutes(1.0);
			timer_0 = new(60000.0);
			timer_0.Elapsed += PrepareTheChronographTimeEndEvent;
			timer_0.Enabled = true;
			timer_0.AutoReset = true;
			PrepareTheChronographTimeEndEvent(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Máu BangChien PrepareTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void PrepareTheChronographTimeEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			var timeSpan = dateTime_0.Subtract(DateTime.Now);
			var num = (int)timeSpan.TotalSeconds;
			using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value in BangChienChuPhuong.DanhSachUngVien.Values)
				{
					value.HelpPrepareAnnouncementPrompt(timeSpan.Minutes.ToString());
				}
			}
			using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value2 in BangChienKhachHang.DanhSachUngVien.Values)
				{
					value2.HelpPrepareAnnouncementPrompt(timeSpan.Minutes.ToString());
				}
			}
			if (num > 0)
			{
				return;
			}
			timer_0.Enabled = false;
			timer_0.Close();
			timer_0.Dispose();
			dateTime_1 = DateTime.Now.AddMinutes(10.0);
			timer_1 = new(1000.0);
			timer_1.Elapsed += StartToFightTheChronographTimeEndEvent;
			timer_1.Enabled = true;
			timer_1.AutoReset = true;
			using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value3 in BangChienChuPhuong.DanhSachUngVien.Values)
				{
					value3.Mobile(787f, -787f, 15f, 7101, 0);
					value3.HelpStartPrompt(0, 0);
				}
			}
			using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value4 in BangChienKhachHang.DanhSachUngVien.Values)
				{
					value4.Mobile(-787f, 787f, 15f, 7101, 0);
					value4.HelpStartPrompt(0, 0);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Máu BangChien PrepareTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void StartToFightTheChronographTimeEndEvent(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = 0;
			var num2 = 0;
			using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value in BangChienChuPhuong.DanhSachUngVien.Values)
				{
					if (value.CloseUp == 1)
					{
						num++;
					}
					value.HelpUpdateScore(ChuPhuong_DiemSo, KhachHang_DiemSo);
				}
			}
			using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
			{
				foreach (var value2 in BangChienKhachHang.DanhSachUngVien.Values)
				{
					if (value2.CloseUp == 1)
					{
						num2++;
					}
					value2.HelpUpdateScore(ChuPhuong_DiemSo, KhachHang_DiemSo);
				}
			}
			if ((int)dateTime_1.Subtract(DateTime.Now).TotalSeconds <= 0)
			{
				KetThuc = 1;
				timer_1.Enabled = false;
				timer_1.Close();
				timer_1.Dispose();
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Máu BangChien StartToFightTheChronographTimeEndEvent error：" + ex);
		}
	}

	public void Dispose()
	{
		if (KetThuc2 == 1)
		{
			return;
		}
		try
		{
			if (ChuPhuong_DiemSo > KhachHang_DiemSo)
			{
				using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value in BangChienChuPhuong.DanhSachUngVien.Values)
					{
						if (value.GangCharacterLevel == 6)
						{
							value.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							GameDb.GuildWar_MoneyBet_Delete(value.AccountID, value.CharacterName, BangChienChuPhuong.DangKy_BangPhaiID, 1);
						}
						if (KetThuc == 1)
						{
							value.HelpStartPrompt(13, 1);
						}
						else if (KetThuc == 2)
						{
							value.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value.HelpStartPrompt(12, 3);
						value.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
				using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value2 in BangChienKhachHang.DanhSachUngVien.Values)
					{
						if (value2.GangCharacterLevel == 6)
						{
							GameDb.GuildWar_MoneyBet_Delete(value2.AccountID, value2.CharacterName, BangChienKhachHang.DangKy_BangPhaiID, -1);
						}
						if (KetThuc == 1)
						{
							value2.HelpStartPrompt(13, -1);
						}
						else if (KetThuc == 2)
						{
							value2.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value2.HelpStartPrompt(12, 3);
						value2.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
			}
			else if (KhachHang_DiemSo > ChuPhuong_DiemSo)
			{
				using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value3 in BangChienChuPhuong.DanhSachUngVien.Values)
					{
						if (value3.GangCharacterLevel == 6)
						{
							GameDb.GuildWar_MoneyBet_Delete(value3.AccountID, value3.CharacterName, BangChienChuPhuong.DangKy_BangPhaiID, -1);
						}
						if (KetThuc == 1)
						{
							value3.HelpStartPrompt(13, -1);
						}
						else if (KetThuc == 2)
						{
							value3.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value3.HelpStartPrompt(12, 3);
						value3.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
				using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value4 in BangChienKhachHang.DanhSachUngVien.Values)
					{
						if (value4.GangCharacterLevel == 6)
						{
							value4.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							GameDb.GuildWar_MoneyBet_Delete(value4.AccountID, value4.CharacterName, BangChienKhachHang.DangKy_BangPhaiID, 1);
						}
						if (KetThuc == 1)
						{
							value4.HelpStartPrompt(13, 1);
						}
						else if (KetThuc == 2)
						{
							value4.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value4.HelpStartPrompt(12, 3);
						value4.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
			}
			else if (KhachHang_DiemSo == ChuPhuong_DiemSo)
			{
				using (new Lock(BangChienChuPhuong.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value5 in BangChienChuPhuong.DanhSachUngVien.Values)
					{
						if (value5.GangCharacterLevel == 6)
						{
							value5.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							GameDb.GuildWar_MoneyBet_Delete(value5.AccountID, value5.CharacterName, BangChienChuPhuong.DangKy_BangPhaiID, 0);
						}
						if (KetThuc == 1)
						{
							value5.HelpStartPrompt(13, 0);
						}
						else if (KetThuc == 2)
						{
							value5.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value5.HelpStartPrompt(12, 3);
						value5.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
				using (new Lock(BangChienKhachHang.DanhSachUngVien, "BangChienKhachHang.DanhSachUngVien"))
				{
					foreach (var value6 in BangChienKhachHang.DanhSachUngVien.Values)
					{
						if (value6.GangCharacterLevel == 6)
						{
							value6.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							GameDb.GuildWar_MoneyBet_Delete(value6.AccountID, value6.CharacterName, BangChienKhachHang.DangKy_BangPhaiID, 0);
						}
						if (KetThuc == 1)
						{
							value6.HelpStartPrompt(13, 0);
						}
						else if (KetThuc == 2)
						{
							value6.HeThongNhacNho("Bang chủ đã rời trận, thoát khỏi chiến trường, đại hội võ lâm buộc phải dừng lại!", 10, "Thiên cơ các");
						}
						value6.HelpStartPrompt(12, 3);
						value6.Mobile(0f, 0f, 15f, 1201, 0);
					}
				}
			}
			LogHelper.WriteLine(LogLevel.Debug, "Bang Chien Ket Thuc MAP_ID:7101 KetThucID:" + KetThuc + " 主BangPhaiID:" + BangChienChuPhuong.DangKy_BangPhaiID + " 主GangName:" + BangChienChuPhuong.DangKyTenBangPhai + " 帮主:" + BangChienChuPhuong.BangChuTen + " 人数:" + BangChienChuPhuong.DanhSachUngVien.Count + " DiemSo:" + ChuPhuong_DiemSo + " ---- 客BangPhaiID:" + BangChienKhachHang.DangKy_BangPhaiID + " 客GangName:" + BangChienKhachHang.DangKyTenBangPhai + " 帮主:" + BangChienKhachHang.BangChuTen + " 人数:" + BangChienKhachHang.DanhSachUngVien.Count + " DiemSo:" + KhachHang_DiemSo);
			KetThuc2 = 1;
			if (timer_0 != null)
			{
				timer_0.Enabled = false;
				timer_0.Close();
				timer_0.Dispose();
			}
			if (timer_1 != null)
			{
				timer_1.Enabled = false;
				timer_1.Close();
				timer_1.Dispose();
			}
			if (BangChienChuPhuong != null)
			{
				if (BangChienChuPhuong.DanhSachUngVien != null)
				{
					BangChienChuPhuong.DanhSachUngVien.Clear();
					BangChienChuPhuong.DanhSachUngVien = null;
				}
				BangChienChuPhuong = null;
			}
			if (BangChienKhachHang != null)
			{
				if (BangChienKhachHang.DanhSachUngVien != null)
				{
					BangChienKhachHang.DanhSachUngVien.Clear();
					BangChienKhachHang.DanhSachUngVien = null;
				}
				BangChienKhachHang = null;
			}
			World.HuyetChien = null;
			KetThuc2 = 1;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Máu BangChien Dispose() error：" + ex);
		}
	}
}
