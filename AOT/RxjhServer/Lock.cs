using System;
using System.Threading;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class Lock : IDisposable
{
	public static int DefaultMillisecondsTimeout = 1000;

	private object _obj;

	public bool IsTimeout => _obj == null;

	public Lock(object obj)
	{
		TryGet(obj, 1000, throwTimeoutException: true, string.Empty);
	}

	public Lock(object obj, string lei)
	{
		TryGet(obj, 1000, throwTimeoutException: true, lei);
	}

	public Lock(object obj, int millisecondsTimeout, bool throwTimeoutException, string lei)
	{
		TryGet(obj, millisecondsTimeout, throwTimeoutException, lei);
	}

	private void TryGet(object obj, int millisecondsTimeout, bool throwTimeoutException, string lei)
	{
		if (Monitor.TryEnter(obj, millisecondsTimeout))
		{
			_obj = obj;
		}
		else
		{
			if (!throwTimeoutException)
			{
				return;
			}
			var type = obj.GetType();
			foreach (var value in World.allConnectedChars.Values)
			{
				LogHelper.WriteLine(LogLevel.Error, "Khóa [Chặt] chờ đối tượng:[" + value.AccountID + "][" + value.CharacterName + "][" + value.SessionID + "]-[" + lei + "]-[" + throwTimeoutException + "]-[" + obj + "]-[" + millisecondsTimeout + "]-[" + type + "]");
			}
		}
	}

	public void Dispose()
	{
		if (_obj != null)
		{
			Monitor.Exit(_obj);
			_obj = null;
		}
	}
}
