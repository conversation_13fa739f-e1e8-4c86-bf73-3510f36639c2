using System.Collections;

namespace RxjhServer;

public class ConfigClass
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	private int int_4;

	private int int_5;

	private int int_6;

	private int int_7;

	private int int_8;

	private int int_9;

	private int int_10;

	private int int_11;

	public int ToDoi
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int GiaoDich
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int TruyenAm
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int YPhuc_BanDau
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public int KiemTraTrangBi
	{
		get
		{
			return int_4;
		}
		set
		{
			int_4 = value;
		}
	}

	public int PetKinhNghiem
	{
		get
		{
			return int_5;
		}
		set
		{
			int_5 = value;
		}
	}

	public int VoHuanSwitchOnOff
	{
		get
		{
			return int_6;
		}
		set
		{
			int_6 = value;
		}
	}

	public int ChuyenDoiToc_OnOff
	{
		get
		{
			return int_7;
		}
		set
		{
			int_7 = value;
		}
	}

	public int ToTinh_SwitchOnOff
	{
		get
		{
			return int_8;
		}
		set
		{
			int_8 = value;
		}
	}

	public int SearchSwitchOnOff
	{
		get
		{
			return int_9;
		}
		set
		{
			int_9 = value;
		}
	}

	public int RauQua_SwitchOnOff
	{
		get
		{
			return int_10;
		}
		set
		{
			int_10 = value;
		}
	}

	public int VinhDu排名HieuQua
	{
		get
		{
			return int_11;
		}
		set
		{
			int_11 = value;
		}
	}

	public static int GetConfig(ConfigClass configClass_0, int int_12)
	{
		var int_13 = 0;
		if (configClass_0.ChuyenDoiToc_OnOff == 1)
		{
			SetFlags(ref int_13, 7, bool_0: true);
		}
		else if (configClass_0.ChuyenDoiToc_OnOff == 3)
		{
			SetFlags(ref int_13, 7, bool_0: true);
		}
		if (int_12 == 801)
		{
			SetFlags(ref int_13, 4, bool_0: true);
		}
		else if (configClass_0.YPhuc_BanDau == 1)
		{
			SetFlags(ref int_13, 4, bool_0: true);
		}
		else if (configClass_0.YPhuc_BanDau == 2)
		{
			SetFlags(ref int_13, 6, bool_0: true);
		}
		return int_13;
	}

	public static void SetFlags(ref int int_12, int int_13, bool bool_0)
	{
		BitArray bitArray = new(new int[1] { int_12 });
		bitArray.Set(int_13, bool_0);
		int_12 = 0;
		for (var i = 0; i < bitArray.Length; i++)
		{
			if (bitArray.Get(i))
			{
				int_12 |= 1 << i;
			}
		}
	}
}
