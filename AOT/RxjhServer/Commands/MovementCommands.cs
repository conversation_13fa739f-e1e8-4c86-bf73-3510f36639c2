using System;
using HeroYulgang.Helpers;
using RxjhServer.Systems;

namespace RxjhServer.Commands
{
    /// <summary>
    /// Admin commands for managing the Movement System
    /// </summary>
    public static class MovementCommands
    {
        /// <summary>
        /// Process movement-related admin commands
        /// </summary>
        public static bool ProcessMovementCommand(Players player, string command, string[] args)
        {
            if (player == null || player.GMMode == 0)
            {
                return false;
            }

            try
            {
                switch (command.ToLower())
                {
                    case "!movementconfig":
                        return HandleMovementConfig(player, args);
                    
                    case "!movementstats":
                        return HandleMovementStats(player, args);
                    
                    case "!movementreset":
                        return HandleMovementReset(player, args);
                    
                    case "!movementtest":
                        return HandleMovementTest(player, args);
                    
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error processing movement command: {ex.Message}");
                player.HeThongNhacNho($"Lỗi xử lý lệnh: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Handle movement configuration commands
        /// Format: !movementconfig [setting] [value]
        /// </summary>
        private static bool HandleMovementConfig(Players player, string[] args)
        {
            if (args.Length == 0)
            {
                // Show current configuration
                var config = $"Movement System Configuration:\n" +
                           $"Speed Tolerance: {MovementSystem.Config.SpeedTolerance:F2}\n" +
                           $"Position Tolerance: {MovementSystem.Config.PositionTolerance:F2}\n" +
                           $"Max Violations/Min: {MovementSystem.Config.MaxViolationsPerMinute}\n" +
                           $"Max Teleport Distance: {MovementSystem.Config.MaxTeleportDistance:F1}\n" +
                           $"Position Correction: {MovementSystem.Config.EnablePositionCorrection}\n" +
                           $"Speed Logging: {MovementSystem.Config.EnableSpeedLogging}\n" +
                           $"Teleport Detection: {MovementSystem.Config.EnableTeleportDetection}";
                
                player.HeThongNhacNho(config);
                return true;
            }

            if (args.Length != 2)
            {
                player.HeThongNhacNho("Sử dụng: !movementconfig [setting] [value]");
                player.HeThongNhacNho("Settings: speedtolerance, positiontolerance, maxteleportdistance, maxviolations");
                return true;
            }

            var setting = args[0].ToLower();
            if (!float.TryParse(args[1], out var value))
            {
                player.HeThongNhacNho("Giá trị phải là số!");
                return true;
            }

            MovementSystem.Config.UpdateConfig(setting, value);
            player.HeThongNhacNho($"Đã cập nhật {setting} = {value}");
            
            LogHelper.WriteLine(LogLevel.Info, $"GM {player.CharacterName} updated movement config: {setting} = {value}");
            return true;
        }

        /// <summary>
        /// Handle movement statistics commands
        /// </summary>
        private static bool HandleMovementStats(Players player, string[] args)
        {
            var stats = MovementSystem.Statistics.GetSummary();
            player.HeThongNhacNho(stats);
            
            // Additional detailed stats if requested
            if (args.Length > 0 && args[0].Equals("detail", StringComparison.CurrentCultureIgnoreCase))
            {
                var violationRate = MovementSystem.Statistics.GetViolationRate();
                var detailStats = $"Detailed Movement Statistics:\n" +
                                $"Violation Rate: {violationRate:F2} violations/hour\n" +
                                $"Speed Violations: {MovementSystem.Statistics.SpeedViolations}\n" +
                                $"Position Violations: {MovementSystem.Statistics.PositionViolations}\n" +
                                $"Teleport Detections: {MovementSystem.Statistics.TeleportDetections}\n" +
                                $"Position Corrections: {MovementSystem.Statistics.PositionCorrections}";
                
                player.HeThongNhacNho(detailStats);
            }
            
            return true;
        }

        /// <summary>
        /// Handle movement statistics reset
        /// </summary>
        private static bool HandleMovementReset(Players player, string[] args)
        {
            if (args.Length == 0)
            {
                player.HeThongNhacNho("Sử dụng: !movementreset [stats|config|all]");
                return true;
            }

            var resetType = args[0].ToLower();
            switch (resetType)
            {
                case "stats":
                    MovementSystem.Statistics.Reset();
                    player.HeThongNhacNho("Đã reset thống kê Movement System");
                    LogHelper.WriteLine(LogLevel.Info, $"GM {player.CharacterName} reset movement statistics");
                    break;
                
                case "config":
                    // Reset to default configuration
                    MovementSystem.Config.SpeedTolerance = 1.15f;
                    MovementSystem.Config.PositionTolerance = 1.25f;
                    MovementSystem.Config.MaxViolationsPerMinute = 8;
                    MovementSystem.Config.MaxTeleportDistance = 200f;
                    MovementSystem.Config.EnablePositionCorrection = true;
                    MovementSystem.Config.EnableSpeedLogging = true;
                    MovementSystem.Config.EnableTeleportDetection = true;
                    
                    player.HeThongNhacNho("Đã reset cấu hình Movement System về mặc định");
                    LogHelper.WriteLine(LogLevel.Info, $"GM {player.CharacterName} reset movement configuration");
                    break;
                
                case "all":
                    MovementSystem.Statistics.Reset();
                    MovementSystem.Config.LoadFromWorld();
                    player.HeThongNhacNho("Đã reset toàn bộ Movement System");
                    LogHelper.WriteLine(LogLevel.Info, $"GM {player.CharacterName} reset entire movement system");
                    break;
                
                default:
                    player.HeThongNhacNho("Tùy chọn không hợp lệ. Sử dụng: stats, config, hoặc all");
                    break;
            }
            
            return true;
        }

        /// <summary>
        /// Handle movement testing commands
        /// </summary>
        private static bool HandleMovementTest(Players player, string[] args)
        {
            if (args.Length == 0)
            {
                player.HeThongNhacNho("Sử dụng: !movementtest [speed|position|teleport]");
                return true;
            }

            var testType = args[0].ToLower();
            switch (testType)
            {
                case "speed":
                    var maxSpeed = MovementSystem.CalculateMaxSpeed(player);
                    player.HeThongNhacNho($"Tốc độ tối đa của bạn: {maxSpeed:F2} pixels/second");
                    player.HeThongNhacNho($"Tốc độ hiện tại: {player.TocDoDiChuyen_Max:F2} pixels/second");
                    break;
                
                case "position":
                    player.HeThongNhacNho($"Vị trí hiện tại: ({player.PosX:F2}, {player.PosY:F2}, {player.PosZ:F2})");
                    player.HeThongNhacNho($"Vị trí đích: ({player.TargetPositionX:F2}, {player.TargetPositionY:F2})");
                    break;
                
                case "teleport":
                    var testDistance = MovementSystem.Config.MaxTeleportDistance;
                    var isTeleport = MovementSystem.IsTeleportDistance(testDistance + 1);
                    player.HeThongNhacNho($"Khoảng cách teleport tối đa: {testDistance:F1}");
                    player.HeThongNhacNho($"Test distance {testDistance + 1:F1} = Teleport: {isTeleport}");
                    break;
                
                default:
                    player.HeThongNhacNho("Test không hợp lệ. Sử dụng: speed, position, hoặc teleport");
                    break;
            }
            
            return true;
        }

        /// <summary>
        /// Get help text for movement commands
        /// </summary>
        public static string GetHelpText()
        {
            return "Movement System Commands:\n" +
                   "!movementconfig - Xem/thay đổi cấu hình\n" +
                   "!movementstats [detail] - Xem thống kê\n" +
                   "!movementreset [stats|config|all] - Reset hệ thống\n" +
                   "!movementtest [speed|position|teleport] - Test chức năng";
        }

        /// <summary>
        /// Check if a command is a movement command
        /// </summary>
        public static bool IsMovementCommand(string command)
        {
            var cmd = command.ToLower();
            return cmd.StartsWith("!movement") || 
                   cmd == "!movementconfig" || 
                   cmd == "!movementstats" || 
                   cmd == "!movementreset" || 
                   cmd == "!movementtest";
        }
    }
}
