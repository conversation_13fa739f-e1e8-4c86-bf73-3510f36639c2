using System;
using System.Security.Cryptography;

namespace RxjhServer;

public static class RNG
{
	private static RandomNumberGenerator rngcryptoServiceProvider_0 = RandomNumberGenerator.Create(); // Use RandomNumberGenerator.Create() for cryptographically secure random numbers

	private static byte[] rb = new byte[4];

	public static int Next()
	{
		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = BitConverter.ToInt32(rb, 0);
		if (num < 0)
		{
			num = -num;
		}
		return num;
	}

	public static int Next(int int_0)
	{
		rngcryptoServiceProvider_0.GetBytes(rb);
		var num = BitConverter.ToInt32(rb, 0) % (int_0 + 1);
		if (num < 0)
		{
			num = -num;
		}
		return num;
	}

	public static int Next(int int_0, int int_1)
	{
		return Next(int_1 - int_0) + int_0;
	}
	 public static long Next(long start, long end)
    {
        if (start > end)
        {
            long temp = start;
            start = end;
            end = temp;
        }
        rngcryptoServiceProvider_0.GetBytes(rb);
        long range = end - start + 1; // Calculate the range
        long num = Math.Abs(BitConverter.ToInt32(rb, 0)); // Convert bytes to an integer
        long result = start + (num % range); // Map to the range [start, end]
    
        return result; // Return as long
    }
}
