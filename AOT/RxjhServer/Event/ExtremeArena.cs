using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using HeroYulgang.Utils;

namespace RxjhServer.Event
{
	// Interface for competition service
	public interface ICompetitionServeice
	{
		bool IsCompetitionStarted();
		bool IsAttackAllowed(ICompetitionPlayer player);
		void StartCompetition();
		void EndCompetition();
		void OnExitGame(ICompetitionPlayer player);
		void OnLeaveField(ICompetitionPlayer player);
		void OnAttackSuccess(ICompetitionPlayer player, int actualDamage);
		void OnKillSuccess(ICompetitionPlayer player, ICompetitionPlayer killedPlayer);
		void OnProcessFullPacket(ICompetitionPlayer player, byte[] fullPacket);
	}

	// Interface for competition world
	public interface ICompetitionWorld
	{
		List<ICompetitionPlayer> GetOnlinePlayerList();
	}

	// Interface for competition player
	public interface ICompetitionPlayer
	{
		string UserName { get; }
		int Player_Level { get; }
		int Player_Job_leve { get; }
		byte Player_Job { get; }
		int Player_Sex { get; }
		int Player_Zx { get; }
		int GuildId { get; }
		string GuildName { get; }
		int SessionID { get; }
		int MapId { get; }
		long ExtremeArenaPoints { get; set; }
		bool ExtremeArenaIsDeath { get; }

		void SendFullPacket(byte[] packet);
		void HeThongNhacNho(string message, int type, string prefix);
		void Mobile(float x, float y, float z, int mapId);
		void ExtremeArenaSetRewards(int result, long totalDamage, int killCount, int deathCount, int participantCount, ref int points, ref int stampCount);
		void ExtremeArenaCentralRevive();
		void ExtremeArenaReviveInPlace();
		void SetUpQuestItem(int itemId, int count);
	}

	// Interface for extreme arena ranking
	public interface IExtremeArenaRanking
	{
		int Ranking { get; set; }
		int Faction { get; set; }
		string CharacterName { get; set; }
		int Player_Level { get; set; }
		string GuildName { get; set; }
		byte Player_Job { get; set; }
		long ArenaPoint { get; set; }
		int GuildId { get; set; }
		int Job_level { get; set; }
		byte Player_Sex { get; set; }
	}

	// Extreme arena ranking implementation
	public class ExtremeArenaRanking : IExtremeArenaRanking
	{
		public int Ranking { get; set; }
		public int Faction { get; set; }
		public string CharacterName { get; set; } = string.Empty;
		public int Player_Level { get; set; }
		public string GuildName { get; set; } = string.Empty;
		public byte Player_Job { get; set; }
		public long ArenaPoint { get; set; }
		public int GuildId { get; set; }
		public int Job_level { get; set; }
		public byte Player_Sex { get; set; }
	}

	// Extreme arena participant
	public class ExtremeArenaParticipant
	{
		public ICompetitionPlayer Player { get; set; }
		public string Name => Player?.UserName ?? string.Empty;
		public int KillCount { get; set; }
		public int DeathCount { get; set; }
		public long TotalDamage { get; set; }
		public int Point { get; set; }
		public int ReviveCount { get; set; } = 5; // Default revive count
		public DateTime PlayerDeathTime { get; set; } = DateTime.MaxValue;
		public DateTime ShowAnimationTime { get; set; } = DateTime.MaxValue;
		public DateTime RemindExitTime { get; set; } = DateTime.MaxValue;

		public ExtremeArenaParticipant(ICompetitionPlayer player)
		{
			Player = player ?? throw new ArgumentNullException(nameof(player));
			KillCount = 0;
			DeathCount = 0;
			TotalDamage = 0;
			Point = 0;
		}
	}

	internal class ExtremeArena : ICompetitionServeice
	{
		public const int SecondsByMilli = 1000;
		public const float WaitingAreaXCoordinate = -4f;
		public const float WaitingAreaYCoordinate = -60f;
		public const int WaitingAreaMapID = 1201;
		public const float CompetitionArenaXCoordinate = 120f;
		public const float CompetitionArenaYCoordinate = 0f;
		public const int CompetitionArenaMapID = 45001;
		public const int ParticipationRequirementLevel = 130;
		public const int ParticipationRequirementLevelJob = 8;
		public const int DefaultMatchPointScore = 5;
		public const int DefaultRewardPoints = 5;
		public const int DefaultDeductionPoints = 5;
		public const int DefaultRewardStampCount = 5;
		private readonly object _opLock;
		private readonly Dictionary<string, DateTime> _lastInvitationTimes;
		private readonly Dictionary<string, ExtremeArenaParticipant> _participants;
		private readonly List<string> _rejecteds;
		private int _participantCount;
		private string _victoryAppearedName;
		private readonly ICompetitionWorld _world;
		private readonly int _maxParticipantCount;
		private bool _isCompetitionStarted;
		private bool _isRegistrationOpen;
		private bool _isHasEnteredArena;
		private bool _isCombatStarted;
		private bool _isExitStarted;
		private Thread _mainThread;
		private bool _mainThreadSwitch;
		private void OnVictoryAppeared(ExtremeArenaParticipant participant)
		{
			lock (_opLock)
			{
				if (_isCombatStarted)
				{
					_victoryAppearedName = participant.Name;
					OnCompetitionEnded();
				}
			}
		}
		private void OnEliminationAppeared(ExtremeArenaParticipant participant)
		{
			lock (_opLock)
			{
				if (!_isCombatStarted)
				{
					return;
				}
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					value.Player.HeThongNhacNho("[" + participant.Name + "]在竞赛中被淘汰了", 10, "极限比武台");
				}
				int points = 5;
				int stampCount = 5;
				participant.Player.ExtremeArenaSetRewards(0, participant.TotalDamage, participant.KillCount, participant.DeathCount, _participantCount, ref points, ref stampCount);
				participant.Player.ExtremeArenaPoints -= points;
				participant.Player.SetUpQuestItem(*********, stampCount);
				CompetitionOpShowAnimationToParticipants(participant, false, points);
			}
		}
		private void OnPlayerRevived(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (_isCombatStarted && _participants.TryGetValue(player.UserName, out value) && value.ReviveCount > 0)
				{
					player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageUpdateReviveCountStatus(player.SessionID, --value.ReviveCount, true));
				}
			}
		}
		private void OnCompetitionEnded()
		{
			lock (_opLock)
			{
				if (!_isCombatStarted)
				{
					return;
				}
				_isCombatStarted = false;
				_isExitStarted = true;
				string judgmentExplanation = "击杀次数最多者获胜";
				if (_victoryAppearedName == null)
				{
					_victoryAppearedName = CompetitionOpDetermineVictoryMethod(ref judgmentExplanation);
				}
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					if (_victoryAppearedName != null && value.Name == _victoryAppearedName)
					{
						int points = 5;
						int stampCount = 5;
						value.Player.ExtremeArenaSetRewards(1, value.TotalDamage, value.KillCount, value.DeathCount, _participantCount, ref points, ref stampCount);
						value.Player.ExtremeArenaPoints += points;
						value.Player.SetUpQuestItem(*********, stampCount);
						CompetitionOpShowAnimationToParticipants(value, true, points);
					}
					else
					{
						int points2 = 5;
						int stampCount2 = 5;
						value.Player.ExtremeArenaSetRewards(0, value.TotalDamage, value.KillCount, value.DeathCount, _participantCount, ref points2, ref stampCount2);
						value.Player.ExtremeArenaPoints -= points2;
						value.Player.SetUpQuestItem(*********, stampCount2);
						CompetitionOpShowAnimationToParticipants(value, false, points2);
					}
					if (_victoryAppearedName != null)
					{
						value.Player.HeThongNhacNho("[" + _victoryAppearedName + "]在竞赛中取得了胜利, 恭喜!", 10, "极限比武台");
					}
					else
					{
						value.Player.HeThongNhacNho("本场未决出胜者, 所有参赛者判负", 10, "极限比武台");
					}
					value.Player.HeThongNhacNho(judgmentExplanation + ")", 10, "(判决说明");
				}
			}
		}
		private void CompetitionOpInviteEligiblePlayersToRegister(int minutesUntilStart)
		{
			lock (_opLock)
			{
				byte[] array;
				switch (minutesUntilStart)
				{
				case 3:
					array = ExtremeArenaPacketFactory.SpMessageCompetitionStartsIn3Minutes();
					break;
				case 2:
					array = ExtremeArenaPacketFactory.SpMessageCompetitionStartsIn2Minutes();
					break;
				default:
					array = null;
					break;
				}
				if (array == null)
				{
					return;
				}
				List<ICompetitionPlayer> onlinePlayerList = _world.GetOnlinePlayerList();
				DateTime now = DateTime.Now;
				foreach (ICompetitionPlayer item in onlinePlayerList)
				{
					if (item.Player_Level < 130 || item.Player_Job_leve < 8 || _participants.ContainsKey(item.UserName) || _rejecteds.Contains(item.UserName))
					{
						continue;
					}
					DateTime value;
					if (_lastInvitationTimes.TryGetValue(item.UserName, out value))
					{
						if (now.Subtract(value).TotalSeconds <= 60.0)
						{
							continue;
						}
						_lastInvitationTimes[item.UserName] = now;
					}
					else
					{
						_lastInvitationTimes.Add(item.UserName, now);
					}
					item.SendFullPacket(array);
				}
			}
		}
		private void CompetitionOpRemindParticipantsToMoveToWaitingArea()
		{
			lock (_opLock)
			{
				byte[] fullPacket = ExtremeArenaPacketFactory.SpMessageCompetitionStartsIn1Minute();
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					if (value.Player.MapId == 1201)
					{
						foreach (ExtremeArenaParticipant value2 in _participants.Values)
						{
							value2.Player.HeThongNhacNho("[" + value2.Name + "]已确认前往等待区域", 10, "极限比武台");
						}
					}
					else
					{
						value.Player.SendFullPacket(fullPacket);
					}
				}
			}
		}
		private void CompetitionOpTeleportParticipantsToCompetitionArena()
		{
			lock (_opLock)
			{
				List<string> list = new List<string>();
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					if (value.Player.MapId != 1201)
					{
						list.Add(value.Name);
					}
				}
				foreach (string item in list)
				{
					foreach (ExtremeArenaParticipant value2 in _participants.Values)
					{
						value2.Player.HeThongNhacNho("[" + item + "]因未到场而被判定为放弃参赛", 10, "极限比武台");
					}
				}
				foreach (string item2 in list)
				{
					_participants.Remove(item2);
				}
				if (_participants.Count < 2)
				{
					byte[] fullPacket = ExtremeArenaPacketFactory.SpMessageMatchFailed();
					foreach (ExtremeArenaParticipant value3 in _participants.Values)
					{
						value3.Player.SendFullPacket(fullPacket);
					}
					_participants.Clear();
					return;
				}
				if (_participants.Count > _maxParticipantCount)
				{
					List<string> list2 = _participants.Keys.ToList();
					int num = _participants.Count - _maxParticipantCount;
					for (int i = num; i < list2.Count; i++)
					{
						_participants[list2[i]].Player.HeThongNhacNho("因参赛人数超限, 报名时间靠后的玩家已无法参赛", 10, "极限比武台");
						_participants.Remove(list2[i]);
					}
				}
				_participantCount = _participants.Count;
				foreach (ExtremeArenaParticipant value4 in _participants.Values)
				{
					value4.Player.Mobile(120f, 0f, 15f, 45001);
					value4.Player.HeThongNhacNho(string.Format("竞赛即将开始, 本场共有{0}人参赛, 请准备迎战", _participantCount), 10, "极限比武台");
				}
			}
		}
		private void CompetitionOpPrepareCountdown()
		{
			lock (_opLock)
			{
				byte[] fullPacket = ExtremeArenaPacketFactory.SpMessageCompetitionCountdown5Seconds();
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					value.Player.SendFullPacket(fullPacket);
					value.Player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageUpdateReviveCountStatus(value.Player.SessionID, value.ReviveCount, true));
				}
			}
		}
		private void CompetitionOpStartCountdown()
		{
			lock (_opLock)
			{
				byte[] fullPacket = ExtremeArenaPacketFactory.SpMessageCompetitionStart();
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					value.Player.SendFullPacket(fullPacket);
				}
			}
		}
		private void CompetitionOpSyncCountdown(int elapsedSeconds)
		{
			lock (_opLock)
			{
				byte[] fullPacket = ExtremeArenaPacketFactory.SpMessageCompetitionCountdownSync(elapsedSeconds);
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					value.Player.SendFullPacket(fullPacket);
				}
			}
		}
		private void CompetitionOpAutoReviveParticipants(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				player.ExtremeArenaCentralRevive();
				OnPlayerRevived(player);
			}
		}
		private void CompetitionOpShowAnimationToParticipants(ExtremeArenaParticipant participant, bool isVictory, int points)
		{
			lock (_opLock)
			{
				participant.ShowAnimationTime = DateTime.Now;
				participant.Player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageCompetitionEndAnimation(isVictory, points));
				participant.Player.SendFullPacket(isVictory ? ExtremeArenaPacketFactory.SpMessageVictoryAdditional() : ExtremeArenaPacketFactory.SpMessageFailureAdditional());
			}
		}
		private void CompetitionOpRemindParticipantsToExit(ExtremeArenaParticipant participant)
		{
			lock (_opLock)
			{
				participant.RemindExitTime = DateTime.Now;
				participant.Player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageRemindPlayerToExit());
			}
		}
		private void CompetitionOpForceParticipantsToExit(ExtremeArenaParticipant participant)
		{
			lock (_opLock)
			{
				if (participant.Player.MapId == 45001)
				{
					participant.Player.Mobile(-4f, -60f, 15f, 1201);
				}
			}
		}
		private string CompetitionOpDetermineVictoryMethod(ref string judgmentExplanation)
		{
			List<ExtremeArenaParticipant> list = _participants.Values.ToList();
			if (list == null || list.Count == 0)
			{
				judgmentExplanation = "出现未知错误";
				return null;
			}
			List<ExtremeArenaParticipant> sortedPlayers = (from p in list
				orderby p.KillCount descending, p.TotalDamage descending, p.Point descending
				select p).ToList();
			List<ExtremeArenaParticipant> maxKillPlayers = sortedPlayers.Where((ExtremeArenaParticipant p) => p.KillCount == sortedPlayers[0].KillCount).ToList();
			if (maxKillPlayers.Count == 1)
			{
				judgmentExplanation = "击杀次数最多者获胜";
				return maxKillPlayers[0].Name;
			}
			List<ExtremeArenaParticipant> maxDamagePlayers = maxKillPlayers.Where((ExtremeArenaParticipant p) => p.TotalDamage == maxKillPlayers.Max((ExtremeArenaParticipant mp) => mp.TotalDamage)).ToList();
			if (maxDamagePlayers.Count == 1)
			{
				judgmentExplanation = "击杀次数相同, 累计伤害最高者获胜";
				return maxDamagePlayers[0].Name;
			}
			List<ExtremeArenaParticipant> list2 = maxDamagePlayers.Where((ExtremeArenaParticipant p) => p.Point == maxDamagePlayers.Max((ExtremeArenaParticipant md) => md.Point)).ToList();
			if (list2.Count == 1)
			{
				judgmentExplanation = "击杀次数相同, 累计伤害相同, 战斗点数最高者获胜";
				return list2[0].Name;
			}
			judgmentExplanation = "击杀次数相同, 累计伤害相同, 战斗点数相同, 所有玩家失败";
			return null;
		}
		public ExtremeArena(ICompetitionWorld world, int maxParticipantCount)
		{
			_opLock = new object();
			_lastInvitationTimes = new Dictionary<string, DateTime>();
			_participants = new Dictionary<string, ExtremeArenaParticipant>();
			_rejecteds = new List<string>();
			_world = world;
			_maxParticipantCount = maxParticipantCount;
		}
		public bool IsCompetitionStarted()
		{
			lock (_opLock)
			{
				return _mainThread != null;
			}
		}
		public bool IsAttackAllowed(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (!_participants.TryGetValue(player.UserName, out value))
				{
					return true;
				}
				if (!_isHasEnteredArena)
				{
					return true;
				}
				if (!_isCombatStarted)
				{
					return false;
				}
				if (value.DeathCount >= 5)
				{
					return false;
				}
				return true;
			}
		}
		public void StartCompetition()
		{
			lock (_opLock)
			{
				if (_mainThread == null)
				{
					_mainThreadSwitch = true;
					_mainThread = new Thread(MainThreadMethod);
					_mainThread.Start();
				}
			}
		}
		private void MainThreadMethod()
		{
			int num = 0;
			List<ExtremeArenaParticipant> list = new List<ExtremeArenaParticipant>();
			while (_mainThreadSwitch)
			{
				lock (_opLock)
				{
					switch (num)
					{
					case 0:
						_isCompetitionStarted = true;
						_isRegistrationOpen = true;
						CompetitionOpInviteEligiblePlayersToRegister(3);
						break;
					case 60:
						CompetitionOpInviteEligiblePlayersToRegister(2);
						break;
					case 120:
						_isRegistrationOpen = false;
						CompetitionOpRemindParticipantsToMoveToWaitingArea();
						break;
					case 180:
						_isHasEnteredArena = true;
						CompetitionOpTeleportParticipantsToCompetitionArena();
						CompetitionOpPrepareCountdown();
						break;
					case 185:
						_isCombatStarted = true;
						CompetitionOpStartCountdown();
						break;
					case 485:
						OnCompetitionEnded();
						break;
					case 600:
						_isCompetitionStarted = false;
						_mainThreadSwitch = false;
						break;
					default:
						if (num < 60)
						{
							CompetitionOpInviteEligiblePlayersToRegister(3);
						}
						else if (num < 120)
						{
							CompetitionOpInviteEligiblePlayersToRegister(2);
						}
						if (num > 185)
						{
							DateTime now = DateTime.Now;
							foreach (ExtremeArenaParticipant value in _participants.Values)
							{
								if (value.Player.ExtremeArenaIsDeath && now.Subtract(value.PlayerDeathTime).TotalSeconds >= 5.0)
								{
									value.PlayerDeathTime = DateTime.MaxValue;
									CompetitionOpAutoReviveParticipants(value.Player);
								}
								if (now.Subtract(value.ShowAnimationTime).TotalSeconds >= 5.0)
								{
									value.ShowAnimationTime = DateTime.MaxValue;
									CompetitionOpRemindParticipantsToExit(value);
								}
								if (now.Subtract(value.RemindExitTime).TotalSeconds >= 10.0)
								{
									value.RemindExitTime = DateTime.MaxValue;
									list.Add(value);
								}
							}
							foreach (ExtremeArenaParticipant item in list)
							{
								CompetitionOpForceParticipantsToExit(item);
							}
							list.Clear();
						}
						if (num >= 190 && num <= 480 && (num - 190) % 5 == 0)
						{
							CompetitionOpSyncCountdown(num - 185);
						}
						break;
					}
				}
				Thread.Sleep(1000);
				num++;
			}
			lock (_opLock)
			{
				_isCompetitionStarted = false;
				_isRegistrationOpen = false;
				_isHasEnteredArena = false;
				_isCombatStarted = false;
				_isExitStarted = false;
				_lastInvitationTimes.Clear();
				foreach (ExtremeArenaParticipant value2 in _participants.Values)
				{
					CompetitionOpForceParticipantsToExit(value2);
				}
				_participants.Clear();
				_rejecteds.Clear();
				_participantCount = 0;
				_victoryAppearedName = null;
				_mainThread = null;
			}
		}
		public void EndCompetition()
		{
			Thread mainThread;
			lock (_opLock)
			{
				if (_mainThread == null)
				{
					return;
				}
				mainThread = _mainThread;
				if (_mainThreadSwitch)
				{
					_mainThreadSwitch = false;
				}
			}
			mainThread.Join();
		}
		public void OnExitGame(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (!_isCompetitionStarted || !_participants.TryGetValue(player.UserName, out value))
				{
					return;
				}
				if (!_isHasEnteredArena)
				{
					_participants.Remove(value.Name);
					_rejecteds.Remove(value.Name);
					{
						foreach (ExtremeArenaParticipant value2 in _participants.Values)
						{
							value2.Player.HeThongNhacNho("[" + value.Name + "]因退出游戏而被判定为放弃参赛", 10, "极限比武台");
						}
						return;
					}
				}
				if (!_isExitStarted && value.DeathCount < 5)
				{
					_participants.Remove(value.Name);
					foreach (ExtremeArenaParticipant value3 in _participants.Values)
					{
						value3.Player.HeThongNhacNho("[" + value.Name + "]因在竞赛过程中退出游戏而被判负", 10, "极限比武台");
					}
					int points = 5;
					int stampCount = 5;
					value.Player.ExtremeArenaSetRewards(0, value.TotalDamage, value.KillCount, value.DeathCount, _participantCount, ref points, ref stampCount);
					value.Player.ExtremeArenaPoints -= points;
					value.Player.SetUpQuestItem(*********, stampCount);
					value.Player.Mobile(-4f, -60f, 15f, 1201);
					if (value.Player.ExtremeArenaIsDeath)
					{
						value.Player.ExtremeArenaReviveInPlace();
					}
					if (_participants.Count == 1)
					{
						OnCompetitionEnded();
					}
					return;
				}
				_participants.Remove(value.Name);
				foreach (ExtremeArenaParticipant value4 in _participants.Values)
				{
					value4.Player.HeThongNhacNho("[" + value.Name + "]已确认退场", 10, "极限比武台");
				}
				value.Player.Mobile(-4f, -60f, 15f, 1201);
				if (value.Player.ExtremeArenaIsDeath)
				{
					value.Player.ExtremeArenaReviveInPlace();
				}
			}
		}
		public void OnLeaveField(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (!_isCompetitionStarted || !_participants.TryGetValue(player.UserName, out value) || !_isHasEnteredArena)
				{
					return;
				}
				if (!_isExitStarted && value.DeathCount < 5)
				{
					_participants.Remove(value.Name);
					foreach (ExtremeArenaParticipant value2 in _participants.Values)
					{
						value2.Player.HeThongNhacNho("[" + value.Name + "]因在竞赛过程中离开场地而被判负", 10, "极限比武台");
					}
					int points = 5;
					int stampCount = 5;
					value.Player.ExtremeArenaSetRewards(0, value.TotalDamage, value.KillCount, value.DeathCount, _participantCount, ref points, ref stampCount);
					value.Player.ExtremeArenaPoints -= points;
					value.Player.SetUpQuestItem(*********, stampCount);
					value.Player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageUpdateReviveCountStatus(value.Player.SessionID, 0, false));
					if (value.Player.ExtremeArenaIsDeath)
					{
						value.Player.ExtremeArenaReviveInPlace();
					}
					if (_participants.Count == 1)
					{
						OnCompetitionEnded();
					}
					return;
				}
				_participants.Remove(value.Name);
				foreach (ExtremeArenaParticipant value3 in _participants.Values)
				{
					value3.Player.HeThongNhacNho("[" + value.Name + "]已确认退场", 10, "极限比武台");
				}
				value.Player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageUpdateReviveCountStatus(value.Player.SessionID, 0, false));
				if (value.Player.ExtremeArenaIsDeath)
				{
					value.Player.ExtremeArenaReviveInPlace();
				}
			}
		}
		public void OnAttackSuccess(ICompetitionPlayer player, int actualDamage)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (_isCompetitionStarted && _isCombatStarted && _participants.TryGetValue(player.UserName, out value))
				{
					value.TotalDamage += actualDamage;
				}
			}
		}
		public void OnKillSuccess(ICompetitionPlayer player, ICompetitionPlayer killedPlayer)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				ExtremeArenaParticipant value2;
				if (!_isCompetitionStarted || !_isCombatStarted || !_participants.TryGetValue(player.UserName, out value) || !_participants.TryGetValue(killedPlayer.UserName, out value2))
				{
					return;
				}
				value.KillCount++;
				value2.DeathCount++;
				value2.PlayerDeathTime = DateTime.Now;
				byte[] fullPacket = ExtremeArenaPacketFactory.SpMessagePlayerDeath(value2.Player.SessionID);
				foreach (ExtremeArenaParticipant value3 in _participants.Values)
				{
					value3.Player.SendFullPacket(fullPacket);
				}
				if (value.KillCount >= 5)
				{
					OnVictoryAppeared(value);
				}
				else if (value2.DeathCount >= 5)
				{
					OnEliminationAppeared(value2);
				}
			}
		}
		private void PlayerOpAgreeOrApplyParticipation(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				if (!_isRegistrationOpen)
				{
					player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageRegistrationTimeout());
					return;
				}
				if (player.Player_Level < 130 || player.Player_Job_leve < 8)
				{
					player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageRegistrationCriteriaNotMet());
					return;
				}
				if (_participants.ContainsKey(player.UserName))
				{
					player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageRegistrationAlreadySuccessful());
					return;
				}
				_participants.Add(player.UserName, new ExtremeArenaParticipant(player));
				player.SendFullPacket(ExtremeArenaPacketFactory.SpMessageRegistrationSuccessful());
				foreach (ExtremeArenaParticipant value in _participants.Values)
				{
					value.Player.HeThongNhacNho(string.Format("欢迎[{0}]参加本次竞赛, 目前已有{1}人参加", player.UserName, _participants.Count), 10, "极限比武台");
				}
			}
		}
		private void PlayerOpDeclineParticipation(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				if (_isRegistrationOpen && player.Player_Level >= 130 && player.Player_Job_leve >= 8 && !_participants.ContainsKey(player.UserName) && !_rejecteds.Contains(player.UserName))
				{
					_rejecteds.Add(player.UserName);
					player.HeThongNhacNho("如果不小心拒绝参赛邀请, 可以到泫勃派找天云岳申请参加", 10, "极限比武台");
				}
			}
		}
		private void PlayerOpConfirmMoveToWaitingArea(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (!_isCompetitionStarted || _isHasEnteredArena || !_participants.TryGetValue(player.UserName, out value))
				{
					return;
				}
				value.Player.Mobile(-4f, -60f, 15f, 1201);
				foreach (ExtremeArenaParticipant value2 in _participants.Values)
				{
					value2.Player.HeThongNhacNho("[" + value.Name + "]已确认前往等待区域", 10, "极限比武台");
				}
			}
		}
		private void PlayerOpConfirmExit(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (_participants.TryGetValue(player.UserName, out value) && value.Player.MapId == 45001 && !(DateTime.Now.Subtract(value.RemindExitTime).TotalSeconds >= 10.0))
				{
					value.RemindExitTime = DateTime.MaxValue;
					value.Player.Mobile(-4f, -60f, 15f, 1201);
				}
			}
		}
		private void PlayerOpChooseReviveInPlace(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (_isCombatStarted && _participants.TryGetValue(player.UserName, out value) && value.Player.ExtremeArenaIsDeath && !(DateTime.Now.Subtract(value.PlayerDeathTime).TotalSeconds >= 5.0))
				{
					value.PlayerDeathTime = DateTime.MaxValue;
					player.ExtremeArenaReviveInPlace();
					OnPlayerRevived(player);
				}
			}
		}
		private void PlayerOpChooseReviveAtCenter(ICompetitionPlayer player)
		{
			lock (_opLock)
			{
				ExtremeArenaParticipant value;
				if (_isCombatStarted && _participants.TryGetValue(player.UserName, out value) && value.Player.ExtremeArenaIsDeath && !(DateTime.Now.Subtract(value.PlayerDeathTime).TotalSeconds >= 5.0))
				{
					value.PlayerDeathTime = DateTime.MaxValue;
					player.ExtremeArenaCentralRevive();
					OnPlayerRevived(player);
				}
			}
		}
		public void OnProcessFullPacket(ICompetitionPlayer player, byte[] fullPacket)
		{
			byte[] array = new byte[fullPacket.Length - 6];
			Buffer.BlockCopy(fullPacket, 4, array, 0, array.Length);
			switch (BitConverter.ToUInt16(array, 4))
			{
			case 1321:
				RpExtremeArenaOp(player, array);
				break;
			case 789:
				RpExtremeArenaTopThreeRankingPodium(player, array);
				break;
			case 345:
				RpExtremeArenaTopList(player, array);
				break;
			}
		}
		private void RpExtremeArenaOp(ICompetitionPlayer player, byte[] data)
		{
			ushort num = BitConverter.ToUInt16(data, 10);
			ushort num2 = BitConverter.ToUInt16(data, 12);
			switch (num)
			{
			case 1:
				switch (num2)
				{
				case 1:
					PlayerOpAgreeOrApplyParticipation(player);
					break;
				case 2:
					PlayerOpDeclineParticipation(player);
					break;
				}
				break;
			case 2:
				if (num2 == 1)
				{
					PlayerOpAgreeOrApplyParticipation(player);
				}
				break;
			case 3:
				if (num2 == 1)
				{
					PlayerOpConfirmMoveToWaitingArea(player);
				}
				break;
			case 6:
				if (num2 == 1)
				{
					PlayerOpConfirmExit(player);
				}
				break;
			case 7:
				switch (num2)
				{
				case 1:
					PlayerOpChooseReviveInPlace(player);
					break;
				case 2:
					PlayerOpChooseReviveAtCenter(player);
					break;
				}
				break;
			case 4:
			case 5:
				break;
			}
		}
		private void RpExtremeArenaTopThreeRankingPodium(ICompetitionPlayer player, byte[] data)
		{
			byte b = data[8];
			byte b2 = data[9];
			byte b3 = data[12];
			if (b == 2 && b2 == 7 && b3 == 1)
			{
				List<ICompetitionPlayer> onlinePlayerList = _world.GetOnlinePlayerList();
				List<ICompetitionPlayer> list = onlinePlayerList.OrderByDescending((ICompetitionPlayer p) => p.ExtremeArenaPoints).ToList();
				List<IExtremeArenaRanking> list2 = new List<IExtremeArenaRanking>();
				for (int i = 0; i < list.Count && i < 3; i++)
				{
					int Ranking = i + 1;
					list2.Add(new ExtremeArenaRanking
					{
						Ranking = Ranking,
						Faction = list[i].Player_Zx,
						CharacterName = list[i].UserName,
						Player_Level = list[i].Player_Level,
						GuildName = ((list[i].GuildId != 0) ? list[i].GuildName : ""),
						Player_Job = list[i].Player_Job,
						ArenaPoint = list[i].ExtremeArenaPoints,
						GuildId = list[i].GuildId,
						Job_level = list[i].Player_Job_leve,
						Player_Sex = (byte)list[i].Player_Sex
                    });
				}
				player.SendFullPacket(ExtremeArenaPacketFactory.SpTopThreeRankingPodium(player.SessionID, list2));
			}
		}
		private void RpExtremeArenaTopList(ICompetitionPlayer player, byte[] data)
		{
			byte b = data[8];
			byte b2 = data[9];
			byte b3 = data[10];
			byte b4 = data[12];
			byte b5 = data[16];
			byte b6 = data[20];
			if (b != 2 || b2 != 7)
			{
				return;
			}
			switch (b4)
			{
			case 1:
			{
				List<ICompetitionPlayer> onlinePlayerList = _world.GetOnlinePlayerList();
				List<ICompetitionPlayer> list = onlinePlayerList.OrderByDescending((ICompetitionPlayer p) => p.ExtremeArenaPoints).ToList();
				List<IExtremeArenaRanking> list2 = new List<IExtremeArenaRanking>();
				for (int i = 0; i < list.Count && i < 100; i++)
				{
					int num = i + 1;
					if ((b5 == 0 && b6 == 0) || (num >= b5 && num <= b6))
					{
						list2.Add(new ExtremeArenaRanking
						{
							Ranking = num,
							Faction = list[i].Player_Zx,
							CharacterName = list[i].UserName,
							Player_Level = list[i].Player_Level,
							GuildName = ((list[i].GuildId != 0) ? list[i].GuildName : ""),
							Player_Job = list[i].Player_Job,
							ArenaPoint = list[i].ExtremeArenaPoints,
							GuildId = list[i].GuildId,
							Job_level = list[i].Player_Job_leve,
							Player_Sex = (byte)list[i].Player_Sex
                        });
					}
				}
				player.SendFullPacket(ExtremeArenaPacketFactory.SpRankingList(player.SessionID, list2));
				break;
			}
			case 2:
				player.SendFullPacket(ExtremeArenaPacketFactory.SpParticipationRecords(player.SessionID));
				break;
			}
		}
	}
}
