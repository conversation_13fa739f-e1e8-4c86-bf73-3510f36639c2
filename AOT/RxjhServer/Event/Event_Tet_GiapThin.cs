using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Tet_GiapThin : IDisposable
{
	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private DateTime dateTime_0;

	private DateTime iqOqpBruKS;

	public Event_Tet_GiapThin()
	{
		World.Event_Tet_GiapThin_Progress = 1;
		World.NpcEvent_Tet_GiapThin.Clear();
		dateTime_0 = DateTime.Now.AddMinutes(5.0);
		ThoiGian1 = new(3000.0);
		ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
		ThoiGian1.Enabled = true;
		ThoiGian1.AutoReset = true;
		var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
		if (num > 0)
		{
			BOSS_Event_Tet_GiapThin();
			{
				foreach (var value in World.allConnectedChars.Values)
				{
					if (!value.Client.TreoMay)
					{
						value.HeThongNhacNho("Sự kiện chém Dưa Hấu khai mở, đại hiệp có [" + num / 60 + "] khắc để chém Dưa Hấu đoạt bảo vật!", 10, "Thiên cơ các");
						GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num);
					}
				}
				return;
			}
		}
		ThoiGianKetThucSuKien1(null, null);
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.Event_Tet_GiapThin_Progress = 2;
			iqOqpBruKS = DateTime.Now.AddMinutes(5.0);
			ThoiGian2 = new(3000.0);
			ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
			ThoiGian2.Enabled = true;
			ThoiGian2.AutoReset = true;
			var num2 = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num2 <= 0)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num2);
					value.HeThongNhacNho("Sự kiện chém Dưa Hấu khép lại sau [" + num2 / 60 + "] khắc, hết giờ bảo vật không còn rơi!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Event ThoiGianKet ThucSuKien 222 Phạm sai lầm：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay && World.Event_Tet_GiapThin_Progress != 0)
				{
					value.HeThongNhacNho("Sự kiện chém Dưa Hấu Tài Lộc khép lại, chúc quần hùng năm mới an khang, thịnh vượng!", 10, "Thiên cơ các");
				}
			}
			World.Event_Tet_GiapThin_Progress = 0;
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			World.Event_Tet_GiapThin_Check.Dispose();
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Event Tet 22 - " + ex.Message);
			World.Event_Tet_GiapThin_Progress = 0;
			World.Event_Tet_GiapThin_Check.Dispose();
			Dispose();
		}
	}

	public void BOSS_Event_Tet_GiapThin()
	{
		try
		{
			var int_ = 15194;
			AddNpc_SoLuong(int_, 420f, 1740f, 15);
			AddNpc_SoLuong(int_, 156f, 1167f, 15);
			AddNpc_SoLuong(int_, 446f, 1172f, 15);
			AddNpc_SoLuong(int_, 346f, 1184f, 15);
			AddNpc_SoLuong(int_, 50f, 718f, 15);
			AddNpc_SoLuong(int_, -436f, 1301f, 15);
			AddNpc_SoLuong(int_, -1084f, 674f, 15);
			AddNpc_SoLuong(int_, -1550f, 41f, 15);
			AddNpc_SoLuong(int_, -1983f, 1210f, 15);
			AddNpc_SoLuong(int_, -708f, -1021f, 15);
			AddNpc_SoLuong(int_, -280f, -420f, 15);
			AddNpc_SoLuong(int_, -22f, 443f, 15);
			AddNpc_SoLuong(int_, 1588f, 598f, 15);
			AddNpc_SoLuong(int_, 2001f, 1086f, 15);
			AddNpc_SoLuong(int_, 1690f, 1930f, 15);
			AddNpc_SoLuong(int_, 2039f, -159f, 15);
			AddNpc_SoLuong(int_, 948f, -833f, 15);
			AddNpc_SoLuong(int_, 192f, -1319f, 15);
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Event năm mới lỗi !! ----------");
		}
	}

	public void AddNpc_SoLuong(int int_0, float float_0, float float_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 500, (int)float_0 + 500);
				var num2 = RNG.Next((int)float_1 - 500, (int)float_1 + 500);
				if (World.MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new()
					{
						FLD_PID = value.fld_pid,
						Name = value.fld_name,
						Level = 1,
						Rxjh_Exp = 0,
						Rxjh_X = num,
						Rxjh_Y = num2,
						Rxjh_Z = 15f,
						Rxjh_cs_X = num,
						Rxjh_cs_Y = num2,
						Rxjh_cs_Z = 15f,
						Rxjh_Map = 101,
						IsNpc = 0,
						FLD_FACE1 = RNG.Next(-1, 1),
						FLD_FACE2 = RNG.Next(-1, 1),
						Max_Rxjh_HP = 1,
						Rxjh_HP = 1,
						FLD_AT = 0.0,
						FLD_DF = 0.0,
						FLD_AUTO = value.fld_auto,
						FLD_BOSS = 0,
						FLD_NEWTIME = 15,
						QuaiXuatHien_DuyNhatMotLan = false,
						timeNpc_HoiSinh = DateTime.MinValue
					};
					if (World.MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.AddNpcToMapClass(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.AddNpcToMapClass(npcClass);
						World.MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.ScanNearbyPlayer();
					World.NpcEvent_Tet_GiapThin.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC số lượng - lỗi 444 [" + int_0 + "]error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.NpcEvent_Tet_GiapThin.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.NpcEvent_Tet_GiapThin.Clear();
			World.Event_Tet_GiapThin_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			if (ThoiGian2 != null)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
			}
			World.Event_Tet_GiapThin_Check = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "EventKetThuc lỗi :" + ex.Message);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}
}
