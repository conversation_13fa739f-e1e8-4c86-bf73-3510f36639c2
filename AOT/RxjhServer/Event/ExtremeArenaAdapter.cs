using System;
using System.Collections.Generic;
using System.Linq;
using RxjhServer;

namespace RxjhServer.Event
{
    /// <summary>
    /// Adapter class to connect Players class with ICompetitionPlayer interface
    /// </summary>
    public class CompetitionPlayerAdapter : ICompetitionPlayer
    {
        private readonly Players _player;

        public CompetitionPlayerAdapter(Players player)
        {
            _player = player ?? throw new ArgumentNullException(nameof(player));
        }

        public string UserName => _player.CharacterName;
        public int Player_Level => _player.Player_Level;
        public int Player_Job_leve => _player.Player_Job_level;
        public byte Player_Job => (byte)_player.Player_Job;
        public int Player_Sex => _player.Player_Sex;
        public int Player_Zx => _player.Player_Zx;
        public int GuildId => _player.GuildId;
        public string GuildName => _player.GuildName ?? string.Empty;
        public int SessionID => _player.SessionID;
        public int MapId => _player.MapID;
        
        public long ExtremeArenaPoints 
        { 
            get => _player.ExtremeArenaPoints; 
            set => _player.ExtremeArenaPoints = value; 
        }
        
        public bool ExtremeArenaIsDeath => _player.ExtremeArenaIsDeath;

        public void SendFullPacket(byte[] packet)
        {
            _player.Client?.Send(packet, packet.Length);
        }

        public void HeThongNhacNho(string message, int type, string prefix)
        {
            _player.HeThongNhacNho(message, type, prefix);
        }

        public void Mobile(float x, float y, float z, int mapId)
        {
            _player.Mobile(x, y, z, mapId,1);
        }

        public void ExtremeArenaSetRewards(int result, long totalDamage, int killCount, int deathCount, int participantCount, ref int points, ref int stampCount)
        {
            _player.ExtremeArenaSetRewards(result, totalDamage, killCount, deathCount, participantCount, ref points, ref stampCount);
        }

        public void ExtremeArenaCentralRevive()
        {
            _player.ExtremeArenaCentralRevive();
        }

        public void ExtremeArenaReviveInPlace()
        {
            _player.ExtremeArenaReviveInPlace();
        }

        public void SetUpQuestItem(int itemId, int count)
        {
            _player.SetUpQuestItems(itemId, count);
        }

        // Implicit conversion from Players to CompetitionPlayerAdapter
        public static implicit operator CompetitionPlayerAdapter(Players player)
        {
            return new CompetitionPlayerAdapter(player);
        }

        // Explicit conversion from CompetitionPlayerAdapter to Players
        public static explicit operator Players(CompetitionPlayerAdapter adapter)
        {
            return adapter._player;
        }
    }

    /// <summary>
    /// Adapter class to connect World class with ICompetitionWorld interface
    /// </summary>
    public class CompetitionWorldAdapter : ICompetitionWorld
    {
        public List<ICompetitionPlayer> GetOnlinePlayerList()
        {
            var players = new List<ICompetitionPlayer>();
            
            foreach (var player in World.allConnectedChars.Values)
            {
                if (player != null && player.Client != null && !player.Client.TreoMay)
                {
                    players.Add(new CompetitionPlayerAdapter(player));
                }
            }
            
            return players;
        }
    }
}
