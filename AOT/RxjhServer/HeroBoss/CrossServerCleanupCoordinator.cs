using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using HeroYulgang.Helpers;

namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Đi<PERSON>u phối cleanup process cho Boss Cross Server để tránh race conditions
    /// </summary>
    public class CrossServerCleanupCoordinator
    {
        private static CrossServerCleanupCoordinator _instance;
        private static readonly object _lock = new();

        // Dictionary lưu trữ cleanup state cho từng boss
        private readonly ConcurrentDictionary<int, CleanupState> _cleanupStates = new();

        // Semaphore để đảm bảo chỉ một cleanup process chạy tại một thời điểm
        private readonly SemaphoreSlim _cleanupSemaphore = new(1, 1);

        public static CrossServerCleanupCoordinator Instance
        {
            get
            {
                if (_instance is null)
                {
                    lock (_lock)
                    {
                        _instance ??= new CrossServerCleanupCoordinator();
                    }
                }
                return _instance;
            }
        }

        private CrossServerCleanupCoordinator()
        {
            LogHelper.WriteLine(LogLevel.Info, "CrossServerCleanupCoordinator initialized");
        }

        /// <summary>
        /// Bắt đầu cleanup process cho boss
        /// </summary>
        public async Task StartCleanupProcess(int bossId, int delaySeconds = 5)
        {
            try
            {
                var cleanupState = _cleanupStates.GetOrAdd(bossId, new CleanupState
                {
                    BossId = bossId,
                    State = CleanupPhase.Pending,
                    StartTime = DateTime.Now
                });

                if (cleanupState.State != CleanupPhase.Pending)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Cleanup already in progress for boss {bossId}, state: {cleanupState.State}");
                    return;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Starting cleanup process for boss {bossId} with {delaySeconds}s delay");

                // Đợi delay trước khi bắt đầu cleanup
                await Task.Delay(delaySeconds * 1000);

                // Thực hiện cleanup
                await ExecuteCleanup(bossId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error starting cleanup process for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Thực hiện cleanup process
        /// </summary>
        private async Task ExecuteCleanup(int bossId)
        {
            await _cleanupSemaphore.WaitAsync();
            try
            {
                if (!_cleanupStates.TryGetValue(bossId, out var cleanupState))
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Cleanup state not found for boss {bossId}");
                    return;
                }

                if (cleanupState.State != CleanupPhase.Pending)
                {
                    LogHelper.WriteLine(LogLevel.Warning, $"Boss {bossId} cleanup already processed, state: {cleanupState.State}");
                    return;
                }

                LogHelper.WriteLine(LogLevel.Info, $"Executing cleanup for boss {bossId}");

                // Phase 1: Đánh dấu bắt đầu cleanup
                cleanupState.State = CleanupPhase.Starting;
                cleanupState.PhaseStartTime = DateTime.Now;

                // Phase 2: Đợi reward distribution hoàn thành
                await WaitForRewardDistribution(bossId);
                cleanupState.State = CleanupPhase.WaitingRewards;

                // Phase 3: Cleanup local data
                await CleanupLocalData(bossId);
                cleanupState.State = CleanupPhase.CleaningLocal;

                // Phase 4: Broadcast cleanup message
                await BroadcastCleanupMessage(bossId);
                cleanupState.State = CleanupPhase.Broadcasting;

                // Phase 5: Final cleanup
                await FinalCleanup(bossId);
                cleanupState.State = CleanupPhase.Completed;
                cleanupState.CompletionTime = DateTime.Now;

                LogHelper.WriteLine(LogLevel.Info, $"Cleanup completed for boss {bossId}");

                // Remove cleanup state sau 1 phút
                _ = Task.Delay(60000).ContinueWith(_ => _cleanupStates.TryRemove(bossId, out var obj));
            }
            finally
            {
                _cleanupSemaphore.Release();
            }
        }

        /// <summary>
        /// Đợi reward distribution hoàn thành
        /// </summary>
        private async Task WaitForRewardDistribution(int bossId)
        {
            try
            {
                // Đợi tối đa 30 giây để reward distribution hoàn thành
                var maxWaitTime = TimeSpan.FromSeconds(30);
                var startTime = DateTime.Now;

                while (DateTime.Now - startTime < maxWaitTime)
                {
                    // Kiểm tra xem reward distribution đã hoàn thành chưa
                    if (IsRewardDistributionCompleted(bossId))
                    {
                        LogHelper.WriteLine(LogLevel.Info, $"Reward distribution completed for boss {bossId}");
                        break;
                    }

                    await Task.Delay(1000); // Đợi 1 giây trước khi kiểm tra lại
                }

                // Đợi thêm 2 giây để đảm bảo
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error waiting for reward distribution for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Kiểm tra reward distribution đã hoàn thành chưa
        /// </summary>
        private bool IsRewardDistributionCompleted(int bossId)
        {
            try
            {
                // Kiểm tra trong WorldBossContribute
                if (World.List_WorldBossContribute.TryGetValue(bossId, out var contribute))
                {
                    return contribute.Rewarded;
                }

                // Nếu không tìm thấy contribute, coi như đã hoàn thành
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error checking reward distribution status for boss {bossId}: {ex.Message}");
                return true; // Assume completed on error
            }
        }

        /// <summary>
        /// Cleanup local data
        /// </summary>
        private async Task CleanupLocalData(int bossId)
        {
            try
            {
                // Cleanup boss từ World.List_WorldBossContribute
                if (World.List_WorldBossContribute.TryGetValue(bossId, out var contribute))
                {
                    // Đánh dấu đã cleanup
                    contribute.Rewarded = true;
                    

                }

                // Cleanup từ CrossServerBossManager
                CrossServerBossManager.Instance.CleanupBoss(bossId);

                LogHelper.WriteLine(LogLevel.Info, $"Local data cleaned up for boss {bossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error cleaning up local data for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Broadcast cleanup message
        /// </summary>
        private async Task BroadcastCleanupMessage(int bossId)
        {
            try
            {
                var message = CrossServerBossProtocol.CreateBossCleanupMessage(bossId);
                World.conn?.Transmit(message);

                LogHelper.WriteLine(LogLevel.Info, $"Broadcasted cleanup message for boss {bossId}");

                // Đợi một chút để message được gửi
                await Task.Delay(1000);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error broadcasting cleanup message for boss {bossId}: {ex.Message}");
            }
        }

        /// <summary>
        /// Final cleanup
        /// </summary>
        private async Task FinalCleanup(int bossId)
        {
            try
            {
                // Remove boss khỏi World.List_WorldBossContribute
                World.List_WorldBossContribute.TryRemove(bossId, out _);



                LogHelper.WriteLine(LogLevel.Info, $"Final cleanup completed for boss {bossId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in final cleanup for boss {bossId}: {ex.Message}");
            }
        }



        /// <summary>
        /// Lấy cleanup state của boss
        /// </summary>
        public CleanupState GetCleanupState(int bossId)
        {
            return _cleanupStates.TryGetValue(bossId, out var state) ? state : null;
        }

        /// <summary>
        /// Hủy cleanup process
        /// </summary>
        public void CancelCleanup(int bossId)
        {
            if (_cleanupStates.TryRemove(bossId, out var state))
            {
                state.State = CleanupPhase.Cancelled;
                LogHelper.WriteLine(LogLevel.Info, $"Cancelled cleanup for boss {bossId}");
            }
        }
    }

    /// <summary>
    /// Trạng thái cleanup
    /// </summary>
    public class CleanupState
    {
        public int BossId { get; set; }
        public CleanupPhase State { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime PhaseStartTime { get; set; }
        public DateTime? CompletionTime { get; set; }
    }

    /// <summary>
    /// Các phase của cleanup process
    /// </summary>
    public enum CleanupPhase
    {
        Pending,
        Starting,
        WaitingRewards,
        CleaningLocal,
        Broadcasting,
        Completed,
        Cancelled
    }
}
