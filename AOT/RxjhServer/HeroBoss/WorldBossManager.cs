using RxjhServer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Helpers;
using YulgangServer;

namespace RxjhServer.HeroBoss
{
    public class WorldBossManager : IDisposable
    {
        private static WorldBossManager _instance;
        private static readonly object _lock = new();
        private bool _isDisposed;

        private readonly List<BossSchedule> bossSchedules = new();
        private readonly Dictionary<int, BossSchedule> activeBosses = new(); // Lưu trữ mối quan hệ giữa boss ID và schedule
        private readonly System.Timers.Timer scheduleTimer;
        private readonly System.Timers.Timer announcementTimer;
        private DateTime lastSpawnTime = DateTime.MinValue;

        public static WorldBossManager Instance
        {
            get
            {
                if (_instance is null)
                {
                    lock (_lock)
                    {
                        _instance ??= new WorldBossManager();
                    }
                }
                return _instance;
            }
        }

        // Constructor chuyển thành private
        private WorldBossManager()
        {
            try
            {
                // Khởi tạo lịch trình boss mặc định
                InitializeDefaultSchedule();

                // Khởi tạo timer để kiểm tra lịch trình
                scheduleTimer = new System.Timers.Timer(5000); // Kiểm tra mỗi 5 giây
                scheduleTimer.Elapsed += CheckSchedule;
                scheduleTimer.AutoReset = true;
                scheduleTimer.Enabled = true;

                // Khởi tạo timer để thông báo trước khi boss xuất hiện
                announcementTimer = new System.Timers.Timer(60000); // Kiểm tra mỗi 1 phút
                announcementTimer.Elapsed += CheckAnnouncement;
                announcementTimer.AutoReset = true;
                announcementTimer.Enabled = true;

                LogHelper.WriteLine(LogLevel.Info, "Thiên cơ các quản lý Boss đã được khởi tạo");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khởi tạo WorldBossManager: {ex.Message}");
                throw;
            }
        }

        // Khởi tạo lịch trình mặc định
        private void InitializeDefaultSchedule()
        {
            // Thêm lịch trình boss thế giới
            AddBossSchedule(9, 0, bossType: BossType.WorldBoss); // 9h sáng
            AddBossSchedule(14, 0, bossType: BossType.WorldBoss); // 2h chiều
            AddBossSchedule(20, 0, bossType: BossType.WorldBoss); // 8h tối
        }

        // Thêm lịch trình boss
        public void AddBossSchedule(int hour, int minute, int mapId = 101, float x = 270f, float y = 1150f,
            int bossId = 15423, int durationMinutes = 30, string name = "", BossType bossType = BossType.WorldBoss, bool isEnabled = true)
        {
            bossSchedules.Add(new BossSchedule
            {
                Hour = hour,
                Minute = minute,
                MapId = mapId,
                X = x,
                Y = y,
                BossId = bossId,
                DurationMinutes = durationMinutes,
                Name = name,
                BossType = bossType,
                IsEnabled = isEnabled
            });

            LogHelper.WriteLine(LogLevel.Info, $"Đã thêm lịch trình {bossType} vào {hour}:{minute:D2}");
        }
        public void CheckSchedule()
        {
            // LogHelper.WriteLine(LogLevel.Info, $"Kiểm tra lịch trình {bossSchedules.Count}");
        }

        // Kiểm tra lịch trình và spawn boss nếu đến giờ
        private void CheckSchedule(object sender, ElapsedEventArgs e)
        {
            var now = DateTime.Now;

           // LogHelper.WriteLine(LogLevel.Info, $"Kiểm tra lịch trình {bossSchedules.Count}");

            foreach (var schedule in bossSchedules.Where(s => s.IsEnabled && !s.IsSpawned && s.ShouldSpawn(now)))
            {
                SpawnBoss(schedule);
                break; // Chỉ spawn một boss mỗi lần
            }
        }

        // Kiểm tra để thông báo trước khi boss xuất hiện
        private void CheckAnnouncement(object sender, ElapsedEventArgs e)
        {
            var now = DateTime.Now;

            // Thông báo trước 5 phút
            var scheduleIn5Min = bossSchedules.Where(s => s.IsEnabled &&
                (s.Hour == now.Hour && s.Minute - now.Minute == 5 ||
                 s.Hour - now.Hour == 1 && s.Minute + 60 - now.Minute == 5));

            foreach (var schedule in scheduleIn5Min)
            {
                AnnounceUpcomingBoss(schedule, 5);
            }

            // Thông báo trước 1 phút
            var scheduleIn1Min = bossSchedules.Where(s => s.IsEnabled &&
                (s.Hour == now.Hour && s.Minute - now.Minute == 1 ||
                 s.Hour - now.Hour == 1 && s.Minute + 60 - now.Minute == 1));

            foreach (var schedule in scheduleIn1Min)
            {
                AnnounceUpcomingBoss(schedule, 1);
            }
        }

        // Thông báo boss sắp xuất hiện
        private void AnnounceUpcomingBoss(BossSchedule schedule, int minutesLeft)
        {
            string bossTypeString = GetBossTypeString(schedule.BossType);

            foreach (var player in World.allConnectedChars.Values)
            {
                player.HeThongNhacNho($"{bossTypeString} sẽ xuất hiện sau {minutesLeft} phút ở bản đồ {schedule.MapId}. Hãy chuẩn bị!");

                if (minutesLeft == 5)
                {
                    // Bắt đầu đếm ngược 1 phút
                    player.GuiDi_TheLucChien_DemNguoc(5*60);
                }
            }
        }

        // Spawn boss
        public void SpawnBoss(BossSchedule schedule)
        {
            try
            {
                // Tạo boss và đánh dấu là boss đặc biệt
                NpcClass boss = World.AddNpcNeo(
                    schedule.BossId,
                    schedule.X,
                    schedule.Y,
                    schedule.MapId,
                    schedule.Name,
                    0, 0, 0, true, 0,
                    schedule.DurationMinutes * 60 * 1000); // Thời gian tồn tại (ms)

                // Đưa vào danh sách quản lý
                World.AddWorldBoss(boss);

                // Khởi tạo boss với loại tương ứng
                switch (schedule.BossType)
                {
                    case BossType.WorldBoss:
                        boss.InitializeAsWorldBoss(schedule.DurationMinutes);
                        break;
                    case BossType.GuildBoss:
                        boss.InitializeAsGuildBoss(schedule.DurationMinutes);
                        break;
                    case BossType.SummonBoss:
                        boss.InitializeAsSummonBoss(schedule.DurationMinutes);
                        break;
                    default:
                        boss.InitializeAsBoss(schedule.BossType, schedule.DurationMinutes);
                        break;
                }
                World.WorldBossEvent.ApplyBossModifiers(boss);
                // Lưu trữ mối quan hệ giữa boss ID và schedule
                activeBosses[boss.ID] = schedule;

                string bossTypeString = GetBossTypeString(schedule.BossType);
                LogHelper.WriteLine(LogLevel.Info, $"Đã spawn {bossTypeString} ID:{boss.ID} tại map {schedule.MapId} vị trí ({schedule.X}, {schedule.Y})");
                schedule.IsSpawned = true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi spawn boss: {ex.Message}");
            }
        }

        // Spawn boss thế giới ngay lập tức (cho lệnh gọi trực tiếp)
        public void SpawnWorldBossNow(int mapId = 101, float x = 270f, float y = 1150f, int bossId = 15423, int durationMinutes = 30, string name = "")
        {
            var schedule = new BossSchedule
            {
                Hour = DateTime.Now.Hour,
                Minute = DateTime.Now.Minute,
                MapId = mapId,
                X = x,
                Y = y,
                BossId = bossId,
                DurationMinutes = durationMinutes,
                Name = name,
                BossType = BossType.WorldBoss,
                IsEnabled = true
            };

            SpawnBoss(schedule);
        }

        // Spawn boss bang hội ngay lập tức (cho lệnh gọi trực tiếp)
        public void SpawnGuildBossNow(int mapId = 101, float x = 270f, float y = 1150f, int bossId = 15424, int durationMinutes = 30, string name = "")
        {
            var schedule = new BossSchedule
            {
                Hour = DateTime.Now.Hour,
                Minute = DateTime.Now.Minute,
                MapId = mapId,
                X = x,
                Y = y,
                BossId = bossId,
                DurationMinutes = durationMinutes,
                Name = name,
                BossType = BossType.GuildBoss,
                IsEnabled = true
            };

            SpawnBoss(schedule);
        }

        // Spawn boss triệu hồi ngay lập tức (cho người chơi triệu hồi)
        public void SpawnSummonBossNow(int mapId, float x, float y, int bossId = 15425, int durationMinutes = 15, string name = "")
        {
            var schedule = new BossSchedule
            {
                Hour = DateTime.Now.Hour,
                Minute = DateTime.Now.Minute,
                MapId = mapId,
                X = x,
                Y = y,
                BossId = bossId,
                DurationMinutes = durationMinutes,
                Name = name,
                BossType = BossType.SummonBoss,
                IsEnabled = true
            };

            SpawnBoss(schedule);
        }

        // Phân phối phần thưởng cho boss
        public void DistributeRewards(int bossId)
        {
            try
            {
                // Gọi hàm phân phối phần thưởng điểm
                World.WorldBoss_TraoThuongPoint(bossId);
                LogHelper.WriteLine(LogLevel.Info, $"Đã phân phối phần thưởng điểm cho boss {bossId}");

                // Đặt IsSpawned thành false cho schedule tương ứng
                ResetBossSchedule(bossId);

                // Lưu ý: Phần phân phối vật phẩm đặc biệt đã được chuyển sang NpcClass.DistributeSpecialRewards
                // Việc xóa boss khỏi danh sách đóng góp được thực hiện trong NpcClass.DistributeRewards
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi phân phối phần thưởng: {ex.Message}");
            }
        }

        // Đặt IsSpawned thành false cho schedule tương ứng với boss
        public void ResetBossSchedule(int bossId)
        {
            try
            {
                if (activeBosses.TryGetValue(bossId, out var schedule))
                {
                    schedule.IsSpawned = false;
                    activeBosses.Remove(bossId);
                    LogHelper.WriteLine(LogLevel.Info, $"Đã đặt lại trạng thái IsSpawned cho boss {bossId}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đặt lại trạng thái IsSpawned: {ex.Message}");
            }
        }

        // Lấy chuỗi mô tả loại boss
        private string GetBossTypeString(BossType bossType)
        {
            return bossType switch
            {
                BossType.WorldBoss => "Boss Thế Giới",
                BossType.GuildBoss => "Boss Bang Hội",
                BossType.SummonBoss => "Boss Triệu Hồi",
                _ => "Boss"
            };
        }

        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                LogHelper.WriteLine(LogLevel.Info, "Thiên cơ các quản lý Boss đang được đóng...");

                // Đặt lại trạng thái IsSpawned cho tất cả các boss đang hoạt động
                foreach (var bossId in activeBosses.Keys.ToList())
                {
                    ResetBossSchedule(bossId);
                }
                activeBosses.Clear();

                if (scheduleTimer != null)
                {
                    scheduleTimer.Enabled = false;
                    scheduleTimer.Dispose();
                }

                if (announcementTimer != null)
                {
                    announcementTimer.Enabled = false;
                    announcementTimer.Dispose();
                }

                _isDisposed = true;
                _instance = null; // Reset singleton instance
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi đóng Thiên cơ các Boss: {ex.Message}");
            }
        }
    }

    public class BossSchedule
    {
        public int Hour { get; set; }
        public int Minute { get; set; }
        public int MapId { get; set; } = 101; // Mặc định là Huyền Bột
        public float X { get; set; } = 270f;
        public float Y { get; set; } = 1150f;
        public int BossId { get; set; } = 15423;
        public int DurationMinutes { get; set; } = 30;
        public string Name { get; set; } = "";
        public BossType BossType { get; set; } = BossType.WorldBoss;
        public bool IsEnabled { get; set; } = true;
        public bool IsSpawned { get; set; } = false;

        public bool ShouldSpawn(DateTime now)
        {
            return now.Hour == Hour && now.Minute == Minute && now.Second < 10;
        }
    }
}
