using System;
using YulgangServer;

namespace RxjhServer.HeroBoss
{
    /// <summary>
    /// Thông tin boss cross-server
    /// </summary>
    public class CrossServerBossInfo
    {
        public int BossId { get; set; }
        public int OriginServerId { get; set; }
        public string BossName { get; set; }
        public int MapId { get; set; }
        public float X { get; set; }
        public float Y { get; set; }
        public int DurationMinutes { get; set; }
        public DateTime SpawnTime { get; set; }
        public DateTime? DeathTime { get; set; }
        public bool IsActive { get; set; }
        public BossType BossType { get; set; } = BossType.WorldBoss;
    }

    /// <summary>
    /// Trạng thái boss cross-server
    /// </summary>
    public enum CrossServerBossState
    {
        Spawning,
        Active,
        Dead,
        Expired,
        Cleanup
    }

    /// <summary>
    /// Đồng bộ trạng thái boss
    /// </summary>
    public class BossStateSync
    {
        public int BossId { get; set; }
        public int CurrentHP { get; set; }
        public int MaxHP { get; set; }
        public CrossServerBossState State { get; set; }
        public DateTime LastUpdate { get; set; }
        public string KillerName { get; set; }
        public DateTime? DeathTime { get; set; }

        public double HPPercentage => MaxHP > 0 ? (double)CurrentHP / MaxHP * 100 : 0;
    }

    /// <summary>
    /// Contribution cross-server với thông tin mở rộng
    /// </summary>
    public class CrossServerDamageContribute
    {
        public int ServerId { get; set; }
        public int SessionId { get; set; }
        public string PlayerName { get; set; }
        public long Damage { get; set; }
        public int AttackCount { get; set; }
        public DateTime FirstAttack { get; set; }
        public DateTime LastUpdate { get; set; }

        public CrossServerDamageContribute(int serverId, int sessionId, string playerName, long damage, int attackCount)
        {
            ServerId = serverId;
            SessionId = sessionId;
            PlayerName = playerName;
            Damage = damage;
            AttackCount = attackCount;
            FirstAttack = DateTime.Now;
            LastUpdate = DateTime.Now;
        }

        public void UpdateDamage(long additionalDamage, int additionalAttacks)
        {
            Damage += additionalDamage;
            AttackCount += additionalAttacks;
            LastUpdate = DateTime.Now;
        }
    }

    /// <summary>
    /// Thông tin reward cross-server
    /// </summary>
    public class CrossServerRewardInfo
    {
        public int BossId { get; set; }
        public int ServerId { get; set; }
        public int SessionId { get; set; }
        public string PlayerName { get; set; }
        public long TotalDamage { get; set; }
        public int AttackCount { get; set; }
        public double DamagePercentage { get; set; }
        public int RewardPoints { get; set; }
        public bool HasSpecialReward { get; set; }
        public string SpecialRewardItem { get; set; }
    }

    /// <summary>
    /// Event args cho boss cross-server events
    /// </summary>
    public class CrossServerBossEventArgs : EventArgs
    {
        public int BossId { get; set; }
        public CrossServerBossState State { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Thông tin spawn boss cross-server
    /// </summary>
    public class CrossServerBossSpawnInfo
    {
        public int BossId { get; set; }
        public int OriginServerId { get; set; }
        public string BossName { get; set; }
        public int MapId { get; set; }
        public float X { get; set; }
        public float Y { get; set; }
        public int MaxHP { get; set; }
        public int DurationMinutes { get; set; }
        public BossType BossType { get; set; }
        public int AccessCondition { get; set; } = 0;
        public int GuildId { get; set; } = 0;
        public float Radius { get; set; } = 500f;
    }

    /// <summary>
    /// Thông tin cập nhật HP boss
    /// </summary>
    public class BossHPUpdateInfo
    {
        public int BossId { get; set; }
        public int CurrentHP { get; set; }
        public int MaxHP { get; set; }
        public int DamageDealt { get; set; }
        public int AttackerServerId { get; set; }
        public int AttackerSessionId { get; set; }
        public string AttackerName { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Thông tin boss death
    /// </summary>
    public class BossDeathInfo
    {
        public int BossId { get; set; }
        public string KillerName { get; set; }
        public int KillerServerId { get; set; }
        public int KillerSessionId { get; set; }
        public DateTime DeathTime { get; set; } = DateTime.Now;
        public long TotalDamageDealt { get; set; }
        public int TotalParticipants { get; set; }
    }
}
