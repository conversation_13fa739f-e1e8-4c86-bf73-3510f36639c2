<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Button Styles -->
    <Style Selector="Button.primary">
        <Setter Property="Background" Value="#007ACC"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#005A9B"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <Style Selector="Button.primary:pointerover">
        <Setter Property="Background" Value="#1F8AD4"/>
    </Style>

    <Style Selector="Button.primary:pressed">
        <Setter Property="Background" Value="#005A9B"/>
    </Style>

    <Style Selector="Button.secondary">
        <Setter Property="Background" Value="#6C757D"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#545B62"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,6"/>
    </Style>

    <Style Selector="Button.success">
        <Setter Property="Background" Value="#28A745"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#1E7E34"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,6"/>
    </Style>

    <Style Selector="Button.danger">
        <Setter Property="Background" Value="#DC3545"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderBrush" Value="#BD2130"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,6"/>
    </Style>

    <Style Selector="Button.warning">
        <Setter Property="Background" Value="#FFC107"/>
        <Setter Property="Foreground" Value="#212529"/>
        <Setter Property="BorderBrush" Value="#E0A800"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="Padding" Value="12,6"/>
    </Style>

    <!-- Tab Styles -->
    <Style Selector="TabControl">
        <Setter Property="Background" Value="Transparent"/>
    </Style>

    <Style Selector="TabItem">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="16,8"/>
    </Style>

    <!-- Panel Styles -->
    <Style Selector="Border.panel">
        <Setter Property="Background" Value="#F8F9FA"/>
        <Setter Property="BorderBrush" Value="#DEE2E6"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="6"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
    </Style>

    <!-- Header Styles -->
    <Style Selector="TextBlock.header">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="#212529"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <Style Selector="TextBlock.subheader">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#495057"/>
        <Setter Property="Margin" Value="0,0,0,8"/>
    </Style>

    <!-- ListBox Styles -->
    <Style Selector="ListBox">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DEE2E6"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <Style Selector="ListBoxItem">
        <Setter Property="Padding" Value="0"/>
        <Setter Property="Margin" Value="0"/>
    </Style>

    <Style Selector="ListBoxItem:selected">
        <Setter Property="Background" Value="#007ACC"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <Style Selector="ListBoxItem:pointerover">
        <Setter Property="Background" Value="#E3F2FD"/>
    </Style>

</Styles>
