-- PostgreSQL function to update character data
-- Function PostgreSQL để cập nhật dữ liệu nhân vật
-- Equivalent to SQL Server XWWL_UPDATE_USER_DATA_NEW procedure

CREATE OR REPLACE FUNCTION xwwl_update_user_data_new(
    p_id VARCHAR(20),
    p_name VARCHAR(20),
    p_level INTEGER,
    p_strface BYTEA,
    p_job INTEGER,
    p_exp VARCHAR(50),
    p_zx INTEGER,
    p_job_level INTEGER,
    p_x DOUBLE PRECISION,
    p_y DOUBLE PRECISION,
    p_z DOUBLE PRECISION,
    p_menow INTEGER,
    p_money VARCHAR(50),
    p_hp INTEGER,
    p_mp INTEGER,
    p_sp INTEGER,
    p_wx INTEGER,
    p_se INTEGER,
    p_point INTEGER,
    p_strskills BYTEA,
    p_strwearitem BYTEA,
    p_stritem BYTEA,
    p_strfaskion BYTEA,
    p_strqusetitem BYTEA,
    p_strkongfu BYTEA,
    p_strctime BYTEA,
    p_strdoors BYTEA,
    p_strquest BYTEA,
    p_strquestfn BYTEA,
    p_fight_exp INTEGER,
    p_rwqg INTEGER,
    p_nametype BYTEA,
    p_zbver INTEGER,
    p_zzlx INTEGER,
    p_zzsld INTEGER,
    p_strctimenew BYTEA,
    p_qlname VARCHAR(20),
    p_qlaqd INTEGER,
    p_qljzname VARCHAR(32),
    p_maritalstatus INTEGER,
    p_maried INTEGER,
    p_loveword VARCHAR(50),
    p_strstskills BYTEA,
    p_strstkongfu BYTEA,
    p_stlilian INTEGER,
    p_stwugongdian INTEGER,
    p_stwugongdianopen INTEGER,
    p_chjh INTEGER,
    p_jhdate TIMESTAMP,
    p_pvpiont INTEGER,
    p_fbtime INTEGER,
    p_hdtime INTEGER,
    p_lostwx INTEGER,
    p_getwx INTEGER,
    p_mgchjh INTEGER,
    p_addhp INTEGER,
    p_addat VARCHAR(500),
    p_adddf VARCHAR(500),
    p_addhb INTEGER,
    p_addmp INTEGER,
    p_addmz INTEGER,
    p_zs INTEGER,
    p_addclvc INTEGER,
    p_addptvc INTEGER,
    p_addkc INTEGER,
    p_mpgxd INTEGER,
    p_whtb INTEGER,
    p_strchtime BYTEA,
    p_strstime BYTEA,
    p_snwugongdian INTEGER,
    p_config VARCHAR(100),
    p_tlc_random VARCHAR(255),
    p_strpinkbag BYTEA,
    p_strantiqigong BYTEA
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Update character data in TBL_XWWL_Char table
    -- Cập nhật dữ liệu nhân vật trong bảng TBL_XWWL_Char
    UPDATE tbl_xwwl_char
    SET 
        fld_level = p_level,
        fld_asc7_anti_qigong = p_strantiqigong,
        fld_pinkbag_item = p_strpinkbag,
        fld_face = p_strface,
        fld_job = p_job,
        fld_exp = p_exp,
        fld_zx = p_zx,
        fld_job_level = p_job_level,
        fld_x = p_x,
        fld_y = p_y,
        fld_z = p_z,
        fld_menow = p_menow,
        fld_money = p_money,
        fld_hp = p_hp,
        fld_mp = p_mp,
        fld_sp = p_sp,
        fld_wx = p_wx,
        fld_point = p_point,
        fld_skills = p_strskills,
        fld_wearitem = p_strwearitem,
        fld_item = p_stritem,
        fld_fashion_item = p_strfaskion,
        fld_qitem = p_strqusetitem,
        fld_kongfu = p_strkongfu,
        fld_fight_exp = p_fight_exp,
        fld_ctime = p_strctime,
        fld_doors = p_strdoors,
        fld_quest = p_strquest,
        fld_jq = p_rwqg,
        fld_se = p_se,
        fld_nametype = p_nametype,
        fld_zbver = p_zbver,
        fld_zztype = p_zzlx,
        fld_zzsl = p_zzsld,
        fld_ctimenew = p_strctimenew,
        fld_qlname = p_qlname,
        fld_love_word = p_loveword,
        fld_stime = p_strstime,
        fld_chtime = p_strchtime,
        fld_qldu = p_qlaqd,
        fld_qljzname = p_qljzname,
        fld_marital_status = p_maritalstatus,
        fld_married = p_maried,
        fld_thangthienkhicong = p_strstskills,
        fld_thangthienvocong = p_strstkongfu,
        fld_thangthienlichluyen = p_stlilian,
        fld_thangthienvocongdiemso = p_stwugongdian,
        fld_mlz = p_stwugongdianopen,
        fld_pvp_piont = p_pvpiont,
        fld_whtb = p_whtb,
        fld_fb_time = p_fbtime,
        fld_hd_time = p_hdtime,
        fld_lost_wx = p_lostwx,
        bangphai_doconghien = p_mpgxd,
        fld_titlepoints = p_chjh,
        fld_add_hp = p_addhp,
        fld_add_at = CAST(p_addat AS INTEGER),
        fld_add_df = CAST(p_adddf AS INTEGER),
        fld_add_hb = p_addhb,
        fld_add_mp = p_addmp,
        fld_add_mz = p_addmz,
        fld_zs = p_zs,
        fld_add_clvc = p_addclvc,
        fld_add_ptvc = p_addptvc,
        fld_add_kc = p_addkc,
        fld_rosetitlepoints = p_mgchjh,
        fld_config = p_config,
        fld_thannuvocongdiemso = p_snwugongdian,
        fld_get_wx = p_getwx,
        fld_quest_finish = p_strquestfn,
        tlc_random_phe = p_tlc_random,
        fld_jh_date = p_jhdate
    WHERE fld_name = p_name AND fld_id = p_id;

    -- Get the number of affected rows
    -- Lấy số dòng bị ảnh hưởng
    GET DIAGNOSTICS affected_rows = ROW_COUNT;

    -- Log the operation
    -- Ghi log thao tác
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Character data updated successfully for player: % (Account: %) - Rows affected: %', p_name, p_id, affected_rows;
    ELSE
        RAISE WARNING 'No character found with name: % and account: %', p_name, p_id;
    END IF;

    -- Return the number of affected rows
    -- Trả về số dòng bị ảnh hưởng
    RETURN affected_rows;

END;
$$;
