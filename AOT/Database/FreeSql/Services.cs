using System;
using System.Linq;
using System.Threading.Tasks;

namespace HeroYulgang.Database.FreeSql
{
    /// <summary>
    /// Simple Services static class - like Node.js modules
    /// Easy access to all database services
    /// </summary>
    public static class Services
    {
        /// <summary>
        /// Public database service for template data
        /// </summary>
        public static class Public
        {
            /// <summary>
            /// Initialize PublicDb and load all templates
            /// Call this from World.SetItme()
            /// </summary>
            public static async Task<bool> InitializeAsync()
            {
                try
                {
                    Console.WriteLine("Initializing Public database services...");
                    
                    // Initialize PublicDb
                    var success = await PublicDb.InitializeAsync();
                    if (!success)
                    {
                        Console.WriteLine("✗ Failed to initialize PublicDb");
                        return false;
                    }

                    // Load templates into World collections for backward compatibility
                    PublicDb.LoadItemTemplatesIntoWorld();
                    PublicDb.LoadMonsterTemplatesIntoWorld();

                    Console.WriteLine("✓ Public database services initialized successfully");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Failed to initialize Public services: {ex.Message}");
                    return false;
                }
            }

            /// <summary>
            /// Refresh all templates and reload into World
            /// Call this for admin refresh commands
            /// </summary>
            public static async Task<bool> RefreshAsync()
            {
                try
                {
                    Console.WriteLine("Refreshing Public database templates...");
                    
                    var success = await PublicDb.RefreshAsync();
                    if (!success)
                    {
                        Console.WriteLine("✗ Failed to refresh PublicDb");
                        return false;
                    }

                    // Reload into World collections
                    PublicDb.LoadItemTemplatesIntoWorld();
                    PublicDb.LoadMonsterTemplatesIntoWorld();

                    Console.WriteLine("✓ Public database templates refreshed successfully");
                    return true;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Failed to refresh Public services: {ex.Message}");
                    return false;
                }
            }

            /// <summary>
            /// Get cache statistics
            /// </summary>
            public static void PrintStats()
            {
                var (itemCount, monsterCount) = PublicDb.GetCacheStats();
                Console.WriteLine($"PublicDb Cache Stats:");
                Console.WriteLine($"  - Items: {itemCount}");
                Console.WriteLine($"  - Monsters: {monsterCount}");
                Console.WriteLine($"  - Total: {itemCount + monsterCount}");
            }

            /// <summary>
            /// Check if service is ready
            /// </summary>
            public static bool IsReady => PublicDb.IsReady;
        }

        /// <summary>
        /// Account database service (placeholder for future)
        /// </summary>
        public static class Account
        {
            public static async Task<bool> InitializeAsync()
            {
                // TODO: Implement AccountDb
                await AccountDb.InitializeAsync();
                AccountDb.RefreshCheckLogin();
                return true;
            }
        }

        /// <summary>
        /// Game database service
        /// </summary>
        public static class Game
        {
            public static async Task<bool> InitializeAsync()
            {
                // Initialize GameDb and load guild members
                await GameDb.InitializeAsync();
                await GameDb.LoadGuildMemberAsync();
                await GameDb.LoadGuildRanking();
                await GameDb.LoadAllRanking();
                return true;
            }
        }

        /// <summary>
        /// BBG/Marketplace database service
        /// </summary>
        public static class BBG
        {
            public static async Task<bool> InitializeAsync()
            {
                BBGDb.Initialize();
                BBGDb.LoadCashShopCategory();
                BBGDb.LoadCashShopItem();
                return true;
            }
        }

        /// <summary>
        /// Initialize all database services
        /// </summary>
        public static async Task<bool> InitializeAllAsync()
        {
            try
            {
                Console.WriteLine("Initializing all database services...");

                var tasks = new[]
                {
                    Public.InitializeAsync(),
                    Account.InitializeAsync(),
                    Game.InitializeAsync(),
                    BBG.InitializeAsync()
                };

                var results = await Task.WhenAll(tasks);
                var success = results.All(r => r);

                if (success)
                {
                    Console.WriteLine("✓ All database services initialized successfully");
                }
                else
                {
                    Console.WriteLine("✗ Some database services failed to initialize");
                }

                return success;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed to initialize database services: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Print status of all services
        /// </summary>
        public static void PrintStatus()
        {
            Console.WriteLine("Database Services Status:");
            Console.WriteLine($"  - Public: {(Public.IsReady ? "Ready" : "Not Ready")}");
            Console.WriteLine($"  - Account: Not Implemented");
            Console.WriteLine($"  - Game: Not Implemented");
            Console.WriteLine($"  - BBG: Not Implemented");
            
            if (Public.IsReady)
            {
                Public.PrintStats();
            }
        }
    }
}
