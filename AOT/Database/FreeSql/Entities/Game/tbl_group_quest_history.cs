﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class tbl_group_quest_history
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public tbl_group_quest_history()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? questid { get; set; }

		[JsonProperty]
		public int? guildid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string guildname { get; set; }

		[JsonProperty]
		public int? factionid { get; set; }

		[JsonProperty]
		public DateTime? completedtime { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string completedby { get; set; }

	}

}
