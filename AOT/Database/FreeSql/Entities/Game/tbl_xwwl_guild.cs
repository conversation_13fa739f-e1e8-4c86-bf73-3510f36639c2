﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class tbl_xwwl_guild
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public tbl_xwwl_guild()
		{
		}

		[JsonProperty, Column(IsPrimary = true)]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2, IsNullable = false)]
		public string g_name { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string g_master { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string g_notice { get; set; }

		[JsonProperty]
		public int? leve { get; set; }

		[JsonProperty]
		public int? thanhdanh { get; set; }

		[JsonProperty]
		public byte[] monhuy { get; set; }

		[JsonProperty]
		public int? monphucword { get; set; }

		[JsonProperty]
		public int? monphucmausac { get; set; }

		[JsonProperty]
		public int? bangphaivohuan { get; set; }

		[JsonProperty]
		public int? thang { get; set; }

		[JsonProperty]
		public int? thua { get; set; }

		[JsonProperty]
		public int? hoa { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string monphaitaisan { get; set; }

		[JsonProperty]
		public int? thongbao_congthanh { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string lienminh_minhchu { get; set; }

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime createdat { get; set; }

		[JsonProperty, Column(InsertValueSql = "now()")]
		public DateTime updatedat { get; set; }

		[JsonProperty]
		public bool active { get; set; } = false;

	}

}
