﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	/// <summary>
	/// Summary view of synthesis activities in the last 30 days - Tổng hợp hoạt động tổng hợp trong 30 ngày qua
	/// </summary>
	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class v_synthesis_summary
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public v_synthesis_summary()
		{
		}

		[JsonProperty, Column(StringLength = 30)]
		public string character_name { get; set; }

		[JsonProperty, Column(StringLength = 30)]
		public string synthesis_type { get; set; }

		[JsonProperty, Column(StringLength = 30)]
		public string result { get; set; }

		[JsonProperty]
		public long? total_attempts { get; set; }

		[JsonProperty]
		public long? successful_attempts { get; set; }

		[JsonProperty]
		public long? failed_attempts { get; set; }

		[JsonProperty, Column(DbType = "numeric")]
		public decimal? success_rate_percent { get; set; }

	}

}
