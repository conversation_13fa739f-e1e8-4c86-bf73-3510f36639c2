﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]	
	public partial class giftcode
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public giftcode()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string username { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string code { get; set; }

		[JsonProperty]
		public int? cash { get; set; }

		[JsonProperty]
		public int? bonus { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string noi_dung { get; set; }

		[JsonProperty]
		public int? da_su_dung { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string nguoi_su_dung { get; set; }

	}

}
