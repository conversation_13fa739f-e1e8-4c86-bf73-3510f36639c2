﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class eventtop_dch
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public eventtop_dch()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tennhanvat { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string bangphai { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string theluc { get; set; }

		[JsonProperty]
		public int? dangcap { get; set; }

		[JsonProperty]
		public int? gietnguoisoluong { get; set; }

		[JsonProperty]
		public int? tuvongsoluong { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string phankhutintuc { get; set; }

		[JsonProperty]
		public int? dame_tru { get; set; }

		[JsonProperty]
		public int? hoptacgietnguoi { get; set; }

		[JsonProperty]
		public int? diem_dch_chinhphai { get; set; }

		[JsonProperty]
		public int? diem_dch_taphai { get; set; }

		[JsonProperty, Column(DbType = "date", InsertValueSql = "now()")]
		public DateTime? createdat { get; set; }

	}

}
