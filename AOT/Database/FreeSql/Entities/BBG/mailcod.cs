﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class mailcod
	{
		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public mailcod()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string sender { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string receiver { get; set; }

		[JsonProperty]
		public byte[] itembyte { get; set; }

		[JsonProperty]
		public int? status { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? expired_at { get; set; }

		[JsonProperty]
		public bool? paid { get; set; }

		[JsonProperty]
		public long? price { get; set; }

		[JsonProperty]
		public byte[] description { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string item_name { get; set; }

	}

}
