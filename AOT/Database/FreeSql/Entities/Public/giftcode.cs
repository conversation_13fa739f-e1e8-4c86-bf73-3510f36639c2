﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class giftcode {

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public giftcode()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string code { get; set; }

		[JsonProperty]
		public int? type { get; set; }

	}

}
