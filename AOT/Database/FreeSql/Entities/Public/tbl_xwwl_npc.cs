﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class tbl_xwwl_npc
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public tbl_xwwl_npc()
		{
		}

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty]
		public double? fld_x { get; set; }

		[JsonProperty]
		public double? fld_z { get; set; }

		[JsonProperty]
		public double? fld_y { get; set; }

		[JsonProperty]
		public double? fld_face0 { get; set; }

		[JsonProperty]
		public double? fld_face { get; set; }

		[JsonProperty]
		public int? fld_mid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public long? fld_hp { get; set; }

		[JsonProperty]
		public long? fld_at { get; set; }

		[JsonProperty]
		public long? fld_df { get; set; }

		[JsonProperty]
		public int? fld_npc { get; set; }

		[JsonProperty]
		public int? fld_newtime { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_exp { get; set; }

		[JsonProperty]
		public int? fld_auto { get; set; }

		[JsonProperty]
		public int? fld_boss { get; set; }

	}

}
