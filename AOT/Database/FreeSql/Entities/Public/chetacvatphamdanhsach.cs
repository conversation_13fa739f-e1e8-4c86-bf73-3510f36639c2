﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class chetacvatphamdanhsach
	{

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public chetacvatphamdanhsach()
		{
		}

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty]
		public int? vatpham_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string vatphamten { get; set; }

		[JsonProperty]
		public int? vatphamsoluong { get; set; }

		[JsonProperty]
		public int? chetaoloaihinh { get; set; }

		[JsonProperty]
		public int? chetaodangcap { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string canvatpham { get; set; }

	}

}
