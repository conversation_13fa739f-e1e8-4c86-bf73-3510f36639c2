﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicConstructors | DynamicallyAccessedMemberTypes.PublicProperties)]
	public partial class tbl_xwwl_drop {

		/// <summary>
		/// Parameterless constructor for AOT compatibility
		/// </summary>
		public tbl_xwwl_drop()
		{
		}

		[JsonProperty]
		public int? fld_level1 { get; set; }

		[JsonProperty]
		public int? fld_level2 { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_magic0 { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_socapphuhon { get; set; }

		[JsonProperty]
		public int? fld_trungcapphuhon { get; set; }

		[JsonProperty]
		public int? fld_tienhoa { get; set; }

		[JsonProperty]
		public int? fld_khoalai { get; set; }

		[JsonProperty]
		public int? fld_pp { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_sunx { get; set; }

		[JsonProperty]
		public int? comothongbao { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

	}

}
