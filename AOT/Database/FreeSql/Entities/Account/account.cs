﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;
using System.Diagnostics.CodeAnalysis;

namespace HeroYulgang.Database.FreeSql.Entities.Account {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class account {

		[JsonProperty]
		public int? id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_password { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string pass2 { get; set; }

		[JsonProperty]
		public int? fld_online { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_card { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_qu { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_answer { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_regip { get; set; }

		[JsonProperty]
		public DateTime? fld_regtime { get; set; }

		[JsonProperty]
		public DateTime? fld_lastlogintime { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_lastloginip { get; set; }

		[JsonProperty]
		public int? fld_vip { get; set; }

		[JsonProperty]
		public int? fld_qsonoff { get; set; }

		[JsonProperty]
		public int? fld_zt { get; set; }

		[JsonProperty]
		public int? fld_sex { get; set; }

		[JsonProperty]
		public int? fld_rxpiont { get; set; }

		[JsonProperty]
		public int? fld_rxpiontx { get; set; }

		[JsonProperty]
		public DateTime? fld_viptim { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_mail { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_fq { get; set; }

		[JsonProperty]
		public int? fld_denbu { get; set; }

		[JsonProperty]
		public int? fld_quayso { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_ghichu { get; set; }

		[JsonProperty]
		public int? fld_diemthuong { get; set; }

		[JsonProperty]
		public int? fld_svip { get; set; }

		[JsonProperty]
		public int? fld_giftcode { get; set; }

		[JsonProperty]
		public int? fld_thelucchien { get; set; }

		[JsonProperty]
		public int? fld_thelucchien2 { get; set; }

		[JsonProperty]
		public int? fld_jf { get; set; }

		[JsonProperty]
		public int? fld_zjf { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_yy { get; set; }

		[JsonProperty]
		public int? fld_bd { get; set; }

		[JsonProperty]
		public int? fld_pay { get; set; }

		[JsonProperty]
		public int? fld_income { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_cardold { get; set; }

		[JsonProperty]
		public int? fld_money { get; set; }

		[JsonProperty]
		public int? fld_totalamount { get; set; }

		[JsonProperty]
		public int? fld_lock { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_safeword { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_spreaderid { get; set; }

		[JsonProperty]
		public int? fld_spreader_level { get; set; }

		[JsonProperty]
		public DateTime? fld_runmore { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_machineid { get; set; }

		[JsonProperty]
		public int? fld_coin { get; set; }

		[JsonProperty]
		public int? fld_transfer_times { get; set; }

		[JsonProperty]
		public int? fld_userid { get; set; }

		[JsonProperty]
		public int? fld_qcvip { get; set; }

		[JsonProperty]
		public DateTime? fld_qcviptim { get; set; }

		[JsonProperty]
		public int? fld_wcvip { get; set; }

		[JsonProperty]
		public DateTime? fld_wcviptim { get; set; }

		[JsonProperty]
		public int? fld_qdcs { get; set; }

		[JsonProperty]
		public DateTime? fld_qdsj { get; set; }

		[JsonProperty]
		public bool? fld_checklogin { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_checkip { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_lanip { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_passkey { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_refresh_key { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_temp_passkey { get; set; }

		[JsonProperty]
		public DateTime? fld_refresh_key_timestamp { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_passkey_timestamp { get; set; }

	}

}
