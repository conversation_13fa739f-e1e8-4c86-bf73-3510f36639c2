using System;
using System.IO;
using System.Text;
using RxjhServer;

namespace HeroYulgang.Utils;

public class SendingClass : IDisposable
{
	private byte[] m_Buffer;

	private int _InfoType;

	private MemoryStream m_Stream;

	private MemoryStream Z_Stream;

	public int InfoType
	{
		get
		{
			return _InfoType;
		}
		set
		{
			_InfoType = value;
		}
	}

	public int Length => (int)(m_Stream.Length + 6);

	public void Dispose()
	{
		m_Stream = null;
		Z_Stream = null;
	}

	public SendingClass()
	{
		m_Buffer = new byte[8];
		m_Stream = new();
	}

	public void Write(byte[] value)
	{
		m_Stream.Write(value, 0, value.Length);
	}

	public void Write(float value)
	{
		m_Buffer = BitConverter.GetBytes(value);
		m_Stream.Write(m_Buffer, 0, 4);
	}

	public void Write(int value)
	{
		m_Stream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write1(int value)
	{
		m_Stream.WriteByte((byte)((uint)value & 0xFFu));
	}

	public void Write2(int value)
	{
		m_Buffer[0] = (byte)value;
		m_Buffer[1] = (byte)(value >> 8);
		m_Stream.Write(m_Buffer, 0, 2);
	}

	public void Write4(int value)
	{
		m_Buffer[0] = (byte)value;
		m_Buffer[1] = (byte)(value >> 8);
		m_Buffer[2] = (byte)(value >> 16);
		m_Buffer[3] = (byte)(value >> 24);
		m_Stream.Write(m_Buffer, 0, 4);
	}

	public string TruncateToByteLength(string input, int maxLength, Encoding encoding)
	{
		var bytes = encoding.GetBytes(input);
		if (bytes.Length <= maxLength)
			return input; // The input is already within the length limit

		// Truncate the string until the byte array fits the maxLength
		var cutLength = input.Length;
		while (bytes.Length > maxLength && cutLength > 0)
		{
			cutLength--;
			bytes = encoding.GetBytes(input.Substring(0, cutLength));
		}

		return input.Substring(0, cutLength);
	}
	public void WriteStringCut(string value, int SoLuong)
	{
		value ??= string.Empty; // Ensure we do not deal with a null value further down

		var truncatedValue = TruncateToByteLength(value, SoLuong, Encoding.GetEncoding(World.Language_Charset));

		// Encode the truncated string into bytes
		var bytes = Encoding.GetEncoding(World.Language_Charset).GetBytes(truncatedValue);

		// Ensure the byte array is exactly the required length
		if (bytes.Length < SoLuong) Array.Resize(ref bytes, SoLuong); // Resize and pad with zeros if necessary

		// Write the bytes to the stream; no need to check for overflow as truncation handles it
		m_Stream.Write(bytes, 0, SoLuong);
	}
	public void WriteStringCut(string value, int SoLuong, Encoding encoding)
	{
		value ??= string.Empty; // Ensure we do not deal with a null value further down

		// Truncate the string to fit the exact byte length allowed
		var truncatedValue = TruncateToByteLength(value, SoLuong, encoding);

		// Encode the truncated string into bytes
		var bytes = encoding.GetBytes(truncatedValue);

		// Ensure the byte array is exactly the required length
		if (bytes.Length < SoLuong) Array.Resize(ref bytes, SoLuong); // Resize and pad with zeros if necessary

		// Write the bytes to the stream; no need to check for overflow as truncation handles it
		m_Stream.Write(bytes, 0, SoLuong);
	}

	public void Write4(uint value)
	{
		m_Buffer[0] = (byte)value;
		m_Buffer[1] = (byte)(value >> 8);
		m_Buffer[2] = (byte)(value >> 16);
		m_Buffer[3] = (byte)(value >> 24);
		m_Stream.Write(m_Buffer, 0, 4);
	}

	public void Write8(long value)
	{
		m_Buffer[0] = (byte)value;
		m_Buffer[1] = (byte)(value >> 8);
		m_Buffer[2] = (byte)(value >> 16);
		m_Buffer[3] = (byte)(value >> 24);
		m_Buffer[4] = (byte)(value >> 32);
		m_Buffer[5] = (byte)(value >> 40);
		m_Buffer[6] = (byte)(value >> 48);
		m_Buffer[7] = (byte)(value >> 56);
		m_Stream.Write(m_Buffer, 0, 8);
	}

	public void Write(long value)
	{
		m_Buffer[0] = (byte)value;
		m_Buffer[1] = (byte)(value >> 8);
		m_Buffer[2] = (byte)(value >> 16);
		m_Buffer[3] = (byte)(value >> 24);
		m_Buffer[4] = (byte)(value >> 32);
		m_Buffer[5] = (byte)(value >> 40);
		m_Buffer[6] = (byte)(value >> 48);
		m_Buffer[7] = (byte)(value >> 56);
		m_Stream.Write(m_Buffer, 0, 8);
	}

	public void Write(byte[] buffer, int offset, int size)
	{
		m_Stream.Write(buffer, offset, size);
	}

	public void WriteAsciiFixed(string value)
	{
		if (value == null)
		{
			Console.WriteLine("Network: Attempted to WriteAsciiFixed() with null value");
			value = string.Empty;
		}
		var bytes = Encoding.Default.GetBytes(value);
		Write2((byte)bytes.Length);
		m_Stream.Write(bytes, 0, bytes.Length);
	}

	public void WriteString16byte(string value)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		var bytes = Encoding.GetEncoding(1252).GetBytes(value);
		var array = new byte[16];
		if (bytes.Length <= 16)
		{
            Buffer.BlockCopy(bytes, 0, array, 0, bytes.Length);
		}
		else
		{
            Buffer.BlockCopy(bytes, 0, array, 0, 16);
		}
		m_Stream.Write(array, 0, array.Length);
	}

	public void WriteString32byte(string value)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		var bytes = Encoding.GetEncoding(1252).GetBytes(value);
		var array = new byte[32];
		if (bytes.Length <= 32)
		{
            Buffer.BlockCopy(bytes, 0, array, 0, bytes.Length);
		}
		else
		{
            Buffer.BlockCopy(bytes, 0, array, 0, 32);
		}
		m_Stream.Write(array, 0, array.Length);
	}

	public void WriteString(string value, int SoLuong)
	{
		if (value == null)
		{
			Console.WriteLine("Network: Attempted to WriteAsciiFixed() with null value");
			value = string.Empty;
		}
		var array = new byte[SoLuong];
		var bytes = Encoding.Default.GetBytes(value);
        Buffer.BlockCopy(bytes, 0, array, 0, bytes.Length);
		m_Stream.Write(array, 0, array.Length);
	}

	public void WriteName(string value)
	{
		if (value == null)
		{
			value = string.Empty;
		}
		var bytes = Encoding.Default.GetBytes(value);
		var array = new byte[15];
		if (bytes.Length <= 15)
		{
            Buffer.BlockCopy(bytes, 0, array, 0, bytes.Length);
		}
		else
		{
            Buffer.BlockCopy(bytes, 0, array, 0, 15);
		}
		m_Stream.Write(array, 0, array.Length);
	}

	public byte[] ToArray3()
	{
		return m_Stream.ToArray();
	}

	public byte[] ToArray2(int aa, int wordid)
	{
		Z_Stream = new();
		m_Buffer[0] = (byte)wordid;
		m_Buffer[1] = (byte)(wordid >> 8);
		m_Buffer[2] = 0;
		m_Buffer[3] = 0;
		Z_Stream.Write(m_Buffer, 0, 4);
		m_Buffer[0] = (byte)(aa >> 8);
		m_Buffer[1] = (byte)aa;
		Z_Stream.Write(m_Buffer, 0, 2);
		m_Buffer[0] = (byte)m_Stream.Length;
		m_Buffer[1] = (byte)(m_Stream.Length >> 8);
		Z_Stream.Write(m_Buffer, 0, 2);
		m_Stream.WriteTo(Z_Stream);
		return Z_Stream.ToArray();
	}

	public byte[] ToArray(int aa, int wordid)
	{
		Z_Stream = new();
		Z_Stream.WriteByte(170);
		Z_Stream.WriteByte(85);
		m_Buffer[0] = (byte)(m_Stream.Length + 7);
		m_Buffer[1] = (byte)(m_Stream.Length + 7 >> 8);
		Z_Stream.Write(m_Buffer, 0, 2);
		Z_Stream.WriteByte(0);
		m_Buffer[0] = (byte)wordid;
		m_Buffer[1] = (byte)(wordid >> 8);
		Z_Stream.Write(m_Buffer, 0, 2);
		m_Buffer[0] = (byte)(aa >> 8);
		m_Buffer[1] = (byte)aa;
		Z_Stream.Write(m_Buffer, 0, 2);
		m_Buffer[0] = (byte)(m_Stream.Length - 8);
		m_Buffer[1] = (byte)(m_Stream.Length - 8 >> 8);
		Z_Stream.Write(m_Buffer, 0, 2);
		Z_Stream.Write(new byte[8], 0, 8);
		m_Stream.WriteTo(Z_Stream);
		Z_Stream.WriteByte(85);
		Z_Stream.WriteByte(170);
		return Z_Stream.ToArray();
	}
}
