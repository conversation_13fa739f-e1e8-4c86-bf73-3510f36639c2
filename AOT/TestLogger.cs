using System;
using System.Threading.Tasks;
using HeroYulgang.Services;

namespace HeroYulgang
{
    public static class TestLogger
    {
        public static void RunTest()
        {
            Console.WriteLine("=== LOGGER TEST START ===");
            
            var logger = Logger.Instance;
            
            Console.WriteLine($"Logger instance created: {logger != null}");
            Console.WriteLine($"Initial logs count: {logger.Logs.Count}");
            
            // Test different log levels
            logger.Debug("🐛 Debug test message");
            logger.Info("ℹ️ Info test message");
            logger.Warning("⚠️ Warning test message");
            logger.Error("❌ Error test message");
            logger.Fatal("💀 Fatal test message");
            
            Console.WriteLine($"After logging - Logs count: {logger.Logs.Count}");
            
            // Wait a bit for batch processing
            Task.Delay(1000).Wait();
            
            Console.WriteLine($"After delay - Logs count: {logger.Logs.Count}");
            
            // Print all logs
            Console.WriteLine("\n=== ALL LOGS ===");
            foreach (var log in logger.Logs)
            {
                Console.WriteLine($"[{log.Timestamp:HH:mm:ss}] [{log.LevelName}] {log.Message}");
            }
            
            Console.WriteLine("=== LOGGER TEST END ===");
        }
    }
}
