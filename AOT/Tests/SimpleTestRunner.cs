using System;
using System.Linq;
using System.Threading.Tasks;

namespace HeroYulgang.Tests
{
    /// <summary>
    /// Simple test runner for new FreeSql system
    /// </summary>
    public class SimpleTestRunner
    {
        /// <summary>
        /// Main entry point for running tests
        /// Usage: dotnet run -- --test-simple
        /// </summary>
        public static async Task<int> Main(string[] args)
        {
            Console.WriteLine("Simple FreeSql Test Runner");
            Console.WriteLine("==========================\n");

            try
            {
                // Check if we should run tests
                if (args.Length == 0 || !args.Contains("--test-simple"))
                {
                    Console.WriteLine("To run simple FreeSql tests, use: --test-simple");
                    Console.WriteLine("\nAvailable test options:");
                    Console.WriteLine("  --test-simple           Run simple FreeSql tests");
                    Console.WriteLine("  --test-init-only        Test initialization only");
                    Console.WriteLine("  --test-refresh-only     Test refresh only");
                    return 0;
                }

                bool allTestsPassed = false;

                if (args.Contains("--test-init-only"))
                {
                    allTestsPassed = await SimpleFreeSqlTest.TestSimpleServicesAsync();
                }
                else if (args.Contains("--test-refresh-only"))
                {
                    // Initialize first, then test refresh
                    await SimpleFreeSqlTest.TestSimpleServicesAsync();
                    allTestsPassed = await SimpleFreeSqlTest.TestRefreshAsync();
                }
                else
                {
                    // Run all tests
                    allTestsPassed = await SimpleFreeSqlTest.RunAllTestsAsync();
                }

                if (allTestsPassed)
                {
                    Console.WriteLine("\n🎉 All tests passed! Simple FreeSql system is ready.");
                    return 0;
                }
                else
                {
                    Console.WriteLine("\n❌ Some tests failed. Please check the configuration and database connectivity.");
                    return 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n💥 Test runner failed with exception: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return 1;
            }
        }

        /// <summary>
        /// Run tests programmatically
        /// </summary>
        public static async Task<bool> RunTestsAsync()
        {
            try
            {
                return await SimpleFreeSqlTest.RunAllTestsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test execution failed: {ex.Message}");
                return false;
            }
        }
    }
}
